/* Estilos para la sección de testimonios */
.testimonials {
    padding: 80px 0;
    background-color: #f8f9fa;
}

.testimonials h2 {
    text-align: center;
    margin-bottom: 50px;
    color: #2d3748;
}

.testimonials-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-bottom: 60px;
}

.testimonial-card {
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.testimonial-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

.testimonial-badge {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #4CAF50;
    font-size: 0.9rem;
    margin-bottom: 20px;
}

.testimonial-badge .material-icons {
    font-size: 1.2rem;
}

.testimonial-card p {
    font-size: 1.1rem;
    line-height: 1.6;
    color: #4a5568;
    margin-bottom: 25px;
}

.testimonial-author {
    display: flex;
    align-items: center;
    gap: 15px;
}

.testimonial-author .avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    object-fit: cover;
}

.testimonial-author h4 {
    margin: 0;
    color: #2d3748;
    font-size: 1.1rem;
}

.testimonial-author p {
    margin: 5px 0 0;
    color: #718096;
    font-size: 0.9rem;
}

.trusted-by {
    text-align: center;
    margin-top: 60px;
}

.trusted-by h3 {
    font-size: 1.5rem;
    color: #2d3748;
    margin-bottom: 30px;
}

.company-logos {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 40px;
    flex-wrap: wrap;
}

.company-logos-grid {
    max-width: 100%;
    height: auto;
    opacity: 0.7;
    transition: opacity 0.3s ease;
}

.company-logos-grid:hover {
    opacity: 1;
}

@media (max-width: 768px) {
    .testimonials-grid {
        grid-template-columns: 1fr;
    }
    
    .company-logos {
        gap: 20px;
    }
}