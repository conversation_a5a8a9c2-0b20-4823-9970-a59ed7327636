* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: Arial, sans-serif;
    background-color: #f0f2f5;
    padding: 20px;
}

.container {
    max-width: 1000px;
    margin: 0 auto;
}

h1 {
    text-align: center;
    color: #1a73e8;
    margin-bottom: 30px;
}

.drop-zone {
    border: 2px dashed #1a73e8;
    border-radius: 10px;
    padding: 40px;
    text-align: center;
    background-color: #fff;
    margin-bottom: 20px;
    transition: border 0.3s ease;
}

.drop-zone.dragover {
    border-color: #34a853;
    background-color: #f8f9fa;
}

#file-input {
    display: none;
}

.upload-button {
    display: inline-block;
    padding: 10px 20px;
    background-color: #1a73e8;
    color: white;
    border-radius: 5px;
    cursor: pointer;
    margin: 10px 0;
}

.upload-button:hover {
    background-color: #1557b0;
}

.preview-section {
    text-align: center;
    margin: 20px 0;
}

#preview-image {
    max-width: 100%;
    max-height: 400px;
    border-radius: 8px;
}

.result-section {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-top: 20px;
}

.text-box {
    background-color: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.text-box h3 {
    margin-bottom: 10px;
    color: #1a73e8;
}

.text-content {
    min-height: 100px;
    padding: 10px;
    background-color: #f8f9fa;
    border-radius: 5px;
    white-space: pre-wrap;
}

.text-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.text-header h3 {
    margin: 0;
}

.copy-button {
    background-color: #1a73e8;
    color: white;
    border: none;
    padding: 5px 15px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.3s ease;
}

.copy-button:hover {
    background-color: #1557b0;
}