<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Netflix Clone</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Helvetica Neue', Arial, sans-serif;
            background-color: #141414;
            color: white;
            overflow-x: hidden;
        }

        /* Header */
        .header {
            position: fixed;
            top: 0;
            width: 100%;
            z-index: 1000;
            padding: 0 4%;
            background: linear-gradient(180deg, rgba(0,0,0,0.7) 10%, transparent);
            transition: background-color 0.3s;
        }

        .header.scrolled {
            background-color: #141414;
        }

        .nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px 0;
        }

        .nav-left {
            display: flex;
            align-items: center;
        }

        .logo {
            color: #e50914;
            font-size: 32px;
            font-weight: bold;
            margin-right: 40px;
        }

        .nav-links {
            display: flex;
            list-style: none;
            gap: 30px;
        }

        .nav-links a {
            color: white;
            text-decoration: none;
            font-size: 14px;
            transition: color 0.3s;
        }

        .nav-links a:hover {
            color: #b3b3b3;
        }

        .nav-right {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .search-icon, .notifications, .profile {
            font-size: 20px;
            cursor: pointer;
        }

        /* Hero Section */
        .hero {
            height: 100vh;
            background: linear-gradient(rgba(0,0,0,0.4), rgba(0,0,0,0.6)), url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 800"><rect fill="%23333" width="1200" height="800"/><text x="600" y="400" font-family="Arial" font-size="40" fill="%23666" text-anchor="middle">Hero Background</text></svg>');
            background-size: cover;
            background-position: center;
            display: flex;
            flex-direction: column;
            justify-content: center;
            padding: 0 4%;
        }

        .hero-content {
            max-width: 500px;
        }

        .hero-title {
            font-size: 48px;
            font-weight: bold;
            margin-bottom: 20px;
        }

        .hero-description {
            font-size: 18px;
            line-height: 1.4;
            margin-bottom: 30px;
        }

        .hero-buttons {
            display: flex;
            gap: 15px;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s;
        }

        .btn-play {
            background-color: white;
            color: black;
        }

        .btn-play:hover {
            background-color: #e6e6e6;
        }

        .btn-info {
            background-color: rgba(109, 109, 110, 0.7);
            color: white;
        }

        .btn-info:hover {
            background-color: rgba(109, 109, 110, 0.4);
        }

        /* Movie Rows */
        .movie-rows {
            padding: 0 4%;
            margin-top: -100px;
            position: relative;
            z-index: 1;
        }

        .movie-row {
            margin-bottom: 40px;
        }

        .row-title {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 15px;
        }

        .movie-slider {
            position: relative;
            overflow: hidden;
        }

        .movie-list {
            display: flex;
            gap: 8px;
            transition: transform 0.5s ease;
        }

        .movie-item {
            min-width: 200px;
            height: 300px;
            position: relative;
            cursor: pointer;
            transition: transform 0.3s ease;
            border-radius: 4px;
            overflow: hidden;
        }

        .movie-item:hover {
            transform: scale(1.05);
            z-index: 10;
        }

        .movie-poster {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 4px;
        }

        .movie-info {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: linear-gradient(transparent, rgba(0,0,0,0.8));
            padding: 20px 10px 10px;
            opacity: 0;
            transition: opacity 0.3s;
        }

        .movie-item:hover .movie-info {
            opacity: 1;
        }

        .movie-title {
            font-size: 14px;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .movie-meta {
            font-size: 12px;
            color: #b3b3b3;
        }

        /* Slider Controls */
        .slider-btn {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            background-color: rgba(0,0,0,0.7);
            color: white;
            border: none;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            font-size: 20px;
            cursor: pointer;
            z-index: 2;
            opacity: 0;
            transition: opacity 0.3s;
        }

        .movie-slider:hover .slider-btn {
            opacity: 1;
        }

        .slider-btn.prev {
            left: 10px;
        }

        .slider-btn.next {
            right: 10px;
        }

        .slider-btn:hover {
            background-color: rgba(0,0,0,0.9);
        }

        /* Footer */
        .footer {
            margin-top: 80px;
            padding: 40px 4%;
            color: #b3b3b3;
            font-size: 14px;
        }

        .footer-content {
            max-width: 1000px;
        }

        .footer-links {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 20px;
            margin-bottom: 30px;
        }

        .footer-links a {
            color: #b3b3b3;
            text-decoration: none;
        }

        .footer-links a:hover {
            text-decoration: underline;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .nav-links {
                display: none;
            }

            .hero-title {
                font-size: 32px;
            }

            .movie-item {
                min-width: 150px;
                height: 225px;
            }

            .footer-links {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header" id="header">
        <nav class="nav">
            <div class="nav-left">
                <div class="logo">NETFLIX</div>
                <ul class="nav-links">
                    <li><a href="#" class="active">Inicio</a></li>
                    <li><a href="#">Series</a></li>
                    <li><a href="#">Películas</a></li>
                    <li><a href="#">Novedades</a></li>
                    <li><a href="#">Mi lista</a></li>
                </ul>
            </div>
            <div class="nav-right">
                <div class="search-icon">🔍</div>
                <div class="notifications">🔔</div>
                <div class="profile">👤</div>
            </div>
        </nav>
    </header>

    <!-- Hero Section -->
    <section class="hero">
        <div class="hero-content">
            <h1 class="hero-title">Stranger Things</h1>
            <p class="hero-description">
                Cuando un niño desaparece, sus amigos, la familia y la policía local se ven envueltos en un misterio que involucra experimentos secretos del gobierno, fuerzas sobrenaturales aterradoras y una niña muy extraña.
            </p>
            <div class="hero-buttons">
                <button class="btn btn-play">▶ Reproducir</button>
                <button class="btn btn-info">ℹ Más información</button>
            </div>
        </div>
    </section>

    <!-- Movie Rows -->
    <div class="movie-rows">
        <!-- Trending Now -->
        <div class="movie-row">
            <h2 class="row-title">Tendencias ahora</h2>
            <div class="movie-slider" data-row="trending">
                <button class="slider-btn prev" onclick="slideMovies('trending', -1)">‹</button>
                <div class="movie-list" id="trending-list">
                    <!-- Movies will be generated by JavaScript -->
                </div>
                <button class="slider-btn next" onclick="slideMovies('trending', 1)">›</button>
            </div>
        </div>

        <!-- Netflix Originals -->
        <div class="movie-row">
            <h2 class="row-title">Originales de Netflix</h2>
            <div class="movie-slider" data-row="originals">
                <button class="slider-btn prev" onclick="slideMovies('originals', -1)">‹</button>
                <div class="movie-list" id="originals-list">
                    <!-- Movies will be generated by JavaScript -->
                </div>
                <button class="slider-btn next" onclick="slideMovies('originals', 1)">›</button>
            </div>
        </div>

        <!-- Action Movies -->
        <div class="movie-row">
            <h2 class="row-title">Películas de acción</h2>
            <div class="movie-slider" data-row="action">
                <button class="slider-btn prev" onclick="slideMovies('action', -1)">‹</button>
                <div class="movie-list" id="action-list">
                    <!-- Movies will be generated by JavaScript -->
                </div>
                <button class="slider-btn next" onclick="slideMovies('action', 1)">›</button>
            </div>
        </div>

        <!-- Horror Movies -->
        <div class="movie-row">
            <h2 class="row-title">Películas de terror</h2>
            <div class="movie-slider" data-row="horror">
                <button class="slider-btn prev" onclick="slideMovies('horror', -1)">‹</button>
                <div class="movie-list" id="horror-list">
                    <!-- Movies will be generated by JavaScript -->
                </div>
                <button class="slider-btn next" onclick="slideMovies('horror', 1)">›</button>
            </div>
        </div>

        <!-- Comedy Movies -->
        <div class="movie-row">
            <h2 class="row-title">Comedias</h2>
            <div class="movie-slider" data-row="comedy">
                <button class="slider-btn prev" onclick="slideMovies('comedy', -1)">‹</button>
                <div class="movie-list" id="comedy-list">
                    <!-- Movies will be generated by JavaScript -->
                </div>
                <button class="slider-btn next" onclick="slideMovies('comedy', 1)">›</button>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="footer">
        <div class="footer-content">
            <div class="footer-links">
                <div>
                    <a href="#">Preguntas frecuentes</a><br>
                    <a href="#">Relaciones con inversionistas</a><br>
                    <a href="#">Formas de ver</a><br>
                    <a href="#">Información corporativa</a>
                </div>
                <div>
                    <a href="#">Centro de ayuda</a><br>
                    <a href="#">Empleo</a><br>
                    <a href="#">Términos de uso</a><br>
                    <a href="#">Contáctanos</a>
                </div>
                <div>
                    <a href="#">Cuenta</a><br>
                    <a href="#">Canjear tarjetas de regalo</a><br>
                    <a href="#">Privacidad</a><br>
                    <a href="#">Prueba de velocidad</a>
                </div>
                <div>
                    <a href="#">Prensa</a><br>
                    <a href="#">Comprar tarjetas de regalo</a><br>
                    <a href="#">Preferencias de cookies</a><br>
                    <a href="#">Avisos legales</a>
                </div>
            </div>
            <p>&copy; 2024 Netflix Clone</p>
        </div>
    </footer>

    <script>
        // Movie data
        const movieData = {
            trending: [
                { title: "Miércoles", year: "2022", rating: "8.1", poster: generatePoster("Miércoles", "#8B1538") },
                { title: "Stranger Things", year: "2016", rating: "8.7", poster: generatePoster("Stranger Things", "#D2001F") },
                { title: "La Casa de Papel", year: "2017", rating: "8.2", poster: generatePoster("La Casa de Papel", "#FF6B6B") },
                { title: "Ozark", year: "2017", rating: "8.4", poster: generatePoster("Ozark", "#4ECDC4") },
                { title: "Dark", year: "2017", rating: "8.8", poster: generatePoster("Dark", "#2C3E50") },
                { title: "The Crown", year: "2016", rating: "8.7", poster: generatePoster("The Crown", "#F39C12") },
                { title: "Narcos", year: "2015", rating: "8.8", poster: generatePoster("Narcos", "#E74C3C") },
                { title: "Breaking Bad", year: "2008", rating: "9.5", poster: generatePoster("Breaking Bad", "#27AE60") }
            ],
            originals: [
                { title: "The Witcher", year: "2019", rating: "8.2", poster: generatePoster("The Witcher", "#9B59B6") },
                { title: "Orange is the New Black", year: "2013", rating: "8.1", poster: generatePoster("OITNB", "#E67E22") },
                { title: "House of Cards", year: "2013", rating: "8.7", poster: generatePoster("House of Cards", "#34495E") },
                { title: "Bridgerton", year: "2020", rating: "7.3", poster: generatePoster("Bridgerton", "#E91E63") },
                { title: "The Umbrella Academy", year: "2019", rating: "7.9", poster: generatePoster("Umbrella Academy", "#3498DB") },
                { title: "Lucifer", year: "2016", rating: "8.1", poster: generatePoster("Lucifer", "#8E44AD") },
                { title: "Atypical", year: "2017", rating: "8.3", poster: generatePoster("Atypical", "#16A085") },
                { title: "The Good Place", year: "2016", rating: "8.2", poster: generatePoster("The Good Place", "#F1C40F") }
            ],
            action: [
                { title: "Extraction", year: "2020", rating: "6.7", poster: generatePoster("Extraction", "#C0392B") },
                { title: "6 Underground", year: "2019", rating: "6.1", poster: generatePoster("6 Underground", "#D35400") },
                { title: "The Old Guard", year: "2020", rating: "6.6", poster: generatePoster("The Old Guard", "#7F8C8D") },
                { title: "Project Power", year: "2020", rating: "6.0", poster: generatePoster("Project Power", "#9C88FF") },
                { title: "Spenser Confidential", year: "2020", rating: "6.2", poster: generatePoster("Spenser", "#1ABC9C") },
                { title: "Triple Frontier", year: "2019", rating: "6.4", poster: generatePoster("Triple Frontier", "#2ECC71") },
                { title: "Bright", year: "2017", rating: "6.3", poster: generatePoster("Bright", "#3498DB") },
                { title: "The Midnight Sky", year: "2020", rating: "5.7", poster: generatePoster("Midnight Sky", "#5D6D7E") }
            ],
            horror: [
                { title: "The Haunting of Hill House", year: "2018", rating: "8.6", poster: generatePoster("Hill House", "#2C3E50") },
                { title: "Bird Box", year: "2018", rating: "6.6", poster: generatePoster("Bird Box", "#34495E") },
                { title: "Gerald's Game", year: "2017", rating: "6.5", poster: generatePoster("Gerald's Game", "#8B4513") },
                { title: "Cam", year: "2018", rating: "5.9", poster: generatePoster("Cam", "#E74C3C") },
                { title: "In the Tall Grass", year: "2019", rating: "5.4", poster: generatePoster("Tall Grass", "#27AE60") },
                { title: "1922", year: "2017", rating: "6.2", poster: generatePoster("1922", "#8B4513") },
                { title: "The Ritual", year: "2017", rating: "6.3", poster: generatePoster("The Ritual", "#2C5F2D") },
                { title: "Hush", year: "2016", rating: "6.6", poster: generatePoster("Hush", "#922B21") }
            ],
            comedy: [
                { title: "The Good Place", year: "2016", rating: "8.2", poster: generatePoster("The Good Place", "#F39C12") },
                { title: "Schitt's Creek", year: "2015", rating: "8.5", poster: generatePoster("Schitt's Creek", "#E67E22") },
                { title: "Space Force", year: "2020", rating: "6.7", poster: generatePoster("Space Force", "#3498DB") },
                { title: "The Politician", year: "2019", rating: "7.5", poster: generatePoster("The Politician", "#9B59B6") },
                { title: "Dead to Me", year: "2019", rating: "8.0", poster: generatePoster("Dead to Me", "#E91E63") },
                { title: "Russian Doll", year: "2019", rating: "7.8", poster: generatePoster("Russian Doll", "#F1C40F") },
                { title: "Big Mouth", year: "2017", rating: "7.8", poster: generatePoster("Big Mouth", "#FF6B6B") },
                { title: "BoJack Horseman", year: "2014", rating: "8.7", poster: generatePoster("BoJack", "#8E44AD") }
            ]
        };

        // Generate poster placeholder
        function generatePoster(title, color) {
            return `data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 300"><rect fill="${color}" width="200" height="300"/><text x="100" y="150" font-family="Arial" font-size="14" fill="white" text-anchor="middle" dominant-baseline="middle">${encodeURIComponent(title)}</text></svg>`;
        }

        // Create movie elements
        function createMovieElement(movie) {
            return `
                <div class="movie-item">
                    <img src="${movie.poster}" alt="${movie.title}" class="movie-poster">
                    <div class="movie-info">
                        <div class="movie-title">${movie.title}</div>
                        <div class="movie-meta">${movie.year} • ⭐ ${movie.rating}</div>
                    </div>
                </div>
            `;
        }

        // Populate movie rows
        function populateMovies() {
            Object.keys(movieData).forEach(category => {
                const listElement = document.getElementById(category + '-list');
                listElement.innerHTML = movieData[category].map(createMovieElement).join('');
            });
        }

        // Slider functionality
        let sliderPositions = {
            trending: 0,
            originals: 0,
            action: 0,
            horror: 0,
            comedy: 0
        };

        function slideMovies(category, direction) {
            const list = document.getElementById(category + '-list');
            const itemWidth = 208; // 200px width + 8px gap
            const maxScroll = -(movieData[category].length - 6) * itemWidth;
            
            sliderPositions[category] += direction * itemWidth * 3;
            
            if (sliderPositions[category] > 0) {
                sliderPositions[category] = 0;
            } else if (sliderPositions[category] < maxScroll) {
                sliderPositions[category] = maxScroll;
            }
            
            list.style.transform = `translateX(${sliderPositions[category]}px)`;
        }

        // Header scroll effect
        window.addEventListener('scroll', () => {
            const header = document.getElementById('header');
            if (window.scrollY > 100) {
                header.classList.add('scrolled');
            } else {
                header.classList.remove('scrolled');
            }
        });

        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            populateMovies();
        });

        // Add click events to buttons
        document.querySelectorAll('.btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                alert(`Funcionalidad de ${e.target.textContent} próximamente!`);
            });
        });
    </script>
</body>
</html>