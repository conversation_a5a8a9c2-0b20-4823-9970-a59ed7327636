<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <title>Tanuki CSS</title>
    <style>
        body {
            background: #cffffc;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
        }
        .tanuki {
            position: relative;
            width: 300px;
            height: 350px;
        }
        .ear {
            position: absolute;
            width: 60px;
            height: 60px;
            background: #b97b3e;
            border-radius: 50%;
            top: 10px;
            z-index: 2;
        }
        .ear.left { left: 10px; }
        .ear.right { right: 10px; }
        .ear-inner {
            position: absolute;
            width: 30px;
            height: 30px;
            background: #ffe4b3;
            border-radius: 50%;
            top: 15px;
            left: 15px;
        }
        .head {
            position: absolute;
            top: 40px;
            left: 25px;
            width: 250px;
            height: 180px;
            background: #b97b3e;
            border-radius: 50% 50% 45% 45% / 55% 55% 45% 45%;
            z-index: 1;
        }
        .face-mask {
            position: absolute;
            top: 90px;
            left: 60px;
            width: 180px;
            height: 70px;
            background: #ffe4b3;
            border-radius: 50% 50% 50% 50% / 60% 60% 40% 40%;
            z-index: 3;
        }
        .eye {
            position: absolute;
            width: 18px;
            height: 18px;
            background: #222;
            border-radius: 50%;
            top: 120px;
            z-index: 4;
        }
        .eye.left { left: 90px; }
        .eye.right { left: 190px; }
        .nose {
            position: absolute;
            top: 145px;
            left: 145px;
            width: 18px;
            height: 14px;
            background: #222;
            border-radius: 50% 50% 60% 60%;
            z-index: 5;
        }
        .mouth {
            position: absolute;
            top: 160px;
            left: 135px;
            width: 40px;
            height: 25px;
            border-bottom: 3px solid #222;
            border-radius: 0 0 40px 40px;
            z-index: 5;
        }
        .body {
            position: absolute;
            top: 170px;
            left: 60px;
            width: 180px;
            height: 150px;
            background: #b97b3e;
            border-radius: 50% 50% 60% 60% / 60% 60% 80% 80%;
            z-index: 0;
        }
        .belly {
            position: absolute;
            top: 210px;
            left: 110px;
            width: 80px;
            height: 90px;
            background: #ffe4b3;
            border-radius: 50% 50% 60% 60%;
            z-index: 2;
        }
        .belly-x {
            position: absolute;
            top: 260px;
            left: 145px;
            width: 20px;
            height: 20px;
            z-index: 3;
        }
        .belly-x:before, .belly-x:after {
            content: '';
            position: absolute;
            width: 20px;
            height: 3px;
            background: #222;
            top: 8px;
            left: 0;
        }
        .belly-x:before {
            transform: rotate(45deg);
        }
        .belly-x:after {
            transform: rotate(-45deg);
        }
        .arm {
            position: absolute;
            width: 90px;
            height: 30px;
            background: #b97b3e;
            border-radius: 50px 50px 40px 40px;
            top: 200px;
            z-index: 1;
        }
        .arm.left {
            left: -60px;
            transform: rotate(-20deg);
        }
        .arm.right {
            right: -60px;
            transform: rotate(20deg);
        }
        .leg {
            position: absolute;
            width: 40px;
            height: 50px;
            background: #b97b3e;
            border-radius: 50% 50% 60% 60%;
            top: 300px;
            z-index: 1;
        }
        .leg.left { left: 80px; }
        .leg.right { left: 180px; }
        .tail {
            position: absolute;
            width: 120px;
            height: 40px;
            background: #b97b3e;
            border-radius: 60px 60px 60px 60px;
            top: 260px;
            left: 220px;
            transform: rotate(20deg);
            z-index: 0;
        }
        .tail-stripe {
            position: absolute;
            width: 30px;
            height: 40px;
            background: #ffe4b3;
            border-radius: 60px 60px 60px 60px;
            top: 0;
            left: 30px;
        }
        .tail-stripe2 {
            position: absolute;
            width: 30px;
            height: 40px;
            background: #ffe4b3;
            border-radius: 60px 60px 60px 60px;
            top: 0;
            left: 70px;
        }
    </style>
</head>
<body>
    <div class="tanuki">
        <div class="ear left"><div class="ear-inner"></div></div>
        <div class="ear right"><div class="ear-inner"></div></div>
        <div class="head"></div>
        <div class="face-mask"></div>
        <div class="eye left"></div>
        <div class="eye right"></div>
        <div class="nose"></div>
        <div class="mouth"></div>
        <div class="body"></div>
        <div class="belly"></div>
        <div class="belly-x"></div>
        <div class="arm left"></div>
        <div class="arm right"></div>
        <div class="leg left"></div>
        <div class="leg right"></div>
        <div class="tail">
            <div class="tail-stripe"></div>
            <div class="tail-stripe2"></div>
        </div>
    </div>
</body>
</html>