<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Carta para el Día de la Madre</title>
    <link href="https://fonts.googleapis.com/css2?family=Dancing+Script:wght@400;700&family=Montserrat:wght@400;500;600&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', 'Arial', sans-serif;
            background: linear-gradient(135deg, #ffccd5 0%, #f9e8e8 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
            overflow-x: hidden;
        }

        .carta {
            position: relative;
            width: 100%;
            max-width: 600px;
            margin: 0 auto;
            perspective: 1500px;
        }

        .sobre {
            position: relative;
            width: 100%;
            height: 450px;
            background: linear-gradient(to bottom, #ffccd5 0%, #ff8fab 100%);
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.25);
            overflow: hidden;
            transition: all 0.5s ease;
            transform-style: preserve-3d;
        }

        .tapa {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(to bottom, #ff8fab 0%, #ff4d6d 100%);
            transform-origin: top;
            transition: transform 1.2s cubic-bezier(0.68, -0.55, 0.27, 1.55);
            z-index: 10;
            display: flex;
            justify-content: center;
            align-items: center;
            border-radius: 15px;
        }

        .tapa::before {
            content: '❤️';
            font-size: 6rem;
            text-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            animation: latido 1.5s infinite alternate;
        }

        @keyframes latido {
            0% {
                transform: scale(1);
            }
            100% {
                transform: scale(1.2);
            }
        }

        .tapa.abierta {
            transform: rotateX(180deg);
        }

        .contenido {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(to bottom, #ffffff 0%, #fff5f7 100%);
            padding: 30px;
            display: flex;
            justify-content: center;
            align-items: center;
            opacity: 0;
            transition: opacity 0.8s ease 0.6s;
            border-radius: 15px;
            overflow-y: auto;
            z-index: 5;
        }

        .contenido.visible {
            opacity: 1 !important;
        }

        .mensaje {
            text-align: center;
            color: #333;
            max-width: 100%;
            width: 100%;
            padding: 0 10px;
        }

        .mensaje h1 {
            color: #d44646;
            margin-top: 20px;
            margin-bottom: 25px;
            font-size: 2rem;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
            animation: colorCambio 5s infinite alternate;
            width: 100%;
            text-align: center;
            word-wrap: break-word;
        }

        @keyframes colorCambio {
            0% {
                color: #ff4d6d;
            }
            50% {
                color: #ff8fab;
            }
            100% {
                color: #ff4d6d;
            }
        }

        #mensaje-personalizado {
            margin: 25px 0;
            line-height: 1.8;
            background-color: rgba(255, 255, 255, 0.8);
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            border: 1px solid #ffccd5;
            width: 100%;
            word-wrap: break-word;
        }

        #mensaje-personalizado p {
            margin-bottom: 18px;
            font-size: 1.1rem;
            color: #444;
            text-shadow: 0 1px 1px rgba(0, 0, 0, 0.05);
            width: 100%;
            word-wrap: break-word;
        }

        .firma {
            font-style: italic;
            margin-top: 5px;
            font-size: 1.3rem;
            color: #ff4d6d;
            text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.1);
            position: relative;
            display: inline-block;
            font-family: 'Dancing Script', cursive;
        }

        .firma::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 0;
            width: 100%;
            height: 2px;
            background: linear-gradient(to right, transparent, #ff4d6d, transparent);
        }

        button {
            display: block;
            margin: 25px auto;
            padding: 15px 30px;
            background: linear-gradient(to right, #ff4d6d, #ff8fab);
            color: white;
            border: none;
            border-radius: 30px;
            font-size: 1.1rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s;
            box-shadow: 0 5px 15px rgba(255, 77, 109, 0.4);
            position: relative;
            overflow: hidden;
        }

        button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transition: 0.5s;
        }

        button:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(255, 77, 109, 0.6);
        }

        button:hover::before {
            left: 100%;
        }

        button:active {
            transform: translateY(0);
            box-shadow: 0 3px 10px rgba(255, 77, 109, 0.4);
        }

        @media (max-width: 600px) {
            .sobre {
                height: 400px;
            }
            
            .mensaje h1 {
                font-size: 1.8rem;
            }
            
            #mensaje-personalizado {
                font-size: 0.95rem;
                padding: 15px;
            }
            
            button {
                padding: 12px 25px;
                font-size: 1rem;
            }
            
            /* Mejoras adicionales para móviles */
            body {
                padding: 10px;
            }
            
            .carta {
                max-width: 100%;
            }
            
            .contenido {
                padding: 15px;
            }
        }

        .sobre::after {
            content: '';
            position: absolute;
            bottom: 10px;
            right: 10px;
            width: 50px;
            height: 50px;
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23ff4d6d"><path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/></svg>');
            background-size: contain;
            background-repeat: no-repeat;
            opacity: 0.5;
            pointer-events: none;
        }
    </style>
</head>
<body>
    <div class="carta">
        <div class="sobre">
            <div class="tapa"></div>
            <div class="contenido">
                <div class="mensaje">
                    <h1>Feliz Día de la Madre</h1>
                    
                    <div id="mensaje-personalizado">
                        <p>Querida Mamá,</p>
                        <p>Gracias por todo tu amor y dedicación. Eres la persona más importante en mi vida y quiero que sepas lo mucho que te quiero.</p>
                        <p>Tu amor incondicional me ha guiado siempre y me ha hecho ser quien soy hoy.</p>
                        <p>¡Te amo mucho!</p>
                    </div>
                    
                    <div class="firma">Con amor, tu hijo</div>
                </div>
            </div>
        </div>
        <button id="boton-abrir">Abrir Carta</button>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const botonAbrir = document.getElementById('boton-abrir');
            const tapa = document.querySelector('.tapa');
            const contenido = document.querySelector('.contenido');
            
            botonAbrir.addEventListener('click', function() {
                tapa.classList.toggle('abierta');
                
                if (botonAbrir.textContent === 'Abrir Carta') {
                    // Mostrar el contenido después de un breve retraso
                    setTimeout(function() {
                        contenido.classList.toggle('visible');
                    }, 600);
                    botonAbrir.textContent = 'Cerrar Carta';
                } else {
                    botonAbrir.textContent = 'Abrir Carta';
                    // Ocultar el contenido después de que la tapa termine de cerrarse (1200ms)
                    setTimeout(function() {
                        contenido.classList.remove('visible');
                    }, 1200);
                }
            });
        });
    </script>
</body>
</html>