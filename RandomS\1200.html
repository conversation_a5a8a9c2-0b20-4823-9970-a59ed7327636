<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard de Productividad</title>

    <!-- 
    =====================================================================
    CSS (HOJA DE ESTILOS EN CASCADA)
    Toda la estilización de la página se encuentra aquí.
    =====================================================================
    -->
    <style>
        /* ------------------------------------------- */
        /*               Variables y Globales          */
        /* ------------------------------------------- */
        
        /* Se definen variables de color para facilitar el cambio de temas y la consistencia. */
        :root {
            --color-bg-light: #f4f7f9;
            --color-surface-light: #ffffff;
            --color-text-primary-light: #2c3e50;
            --color-text-secondary-light: #7f8c8d;
            --color-accent-light: #3498db;
            --color-accent-hover-light: #2980b9;
            --color-border-light: #ecf0f1;
            --color-success-light: #2ecc71;
            --color-danger-light: #e74c3c;
            --shadow-light: 0 4px 15px rgba(0, 0, 0, 0.08);

            --color-bg-dark: #2c3e50;
            --color-surface-dark: #34495e;
            --color-text-primary-dark: #ecf0f1;
            --color-text-secondary-dark: #bdc3c7;
            --color-accent-dark: #5dade2;
            --color-accent-hover-dark: #85c1e9;
            --color-border-dark: #46627f;
            --color-success-dark: #27ae60;
            --color-danger-dark: #c0392b;
            --shadow-dark: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        /* Estilos base para el modo claro (por defecto) */
        body {
            --color-bg: var(--color-bg-light);
            --color-surface: var(--color-surface-light);
            --color-text-primary: var(--color-text-primary-light);
            --color-text-secondary: var(--color-text-secondary-light);
            --color-accent: var(--color-accent-light);
            --color-accent-hover: var(--color-accent-hover-light);
            --color-border: var(--color-border-light);
            --color-success: var(--color-success-light);
            --color-danger: var(--color-danger-light);
            --shadow: var(--shadow-light);
        }

        /* Estilos para el modo oscuro, aplicados cuando el body tiene la clase 'dark-mode' */
        body.dark-mode {
            --color-bg: var(--color-bg-dark);
            --color-surface: var(--color-surface-dark);
            --color-text-primary: var(--color-text-primary-dark);
            --color-text-secondary: var(--color-text-secondary-dark);
            --color-accent: var(--color-accent-dark);
            --color-accent-hover: var(--color-accent-hover-dark);
            --color-border: var(--color-border-dark);
            --color-success: var(--color-success-dark);
            --color-danger: var(--color-danger-dark);
            --shadow: var(--shadow-dark);
        }

        /* Reseteo y estilos globales */
        *,
        *::before,
        *::after {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        html {
            font-size: 16px;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
            background-color: var(--color-bg);
            color: var(--color-text-primary);
            line-height: 1.6;
            transition: background-color 0.3s ease, color 0.3s ease;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        /* ------------------------------------------- */
        /*                   Layout                    */
        /* ------------------------------------------- */
        
        .main-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem 1.5rem;
        }
        
        /* Cabecera principal */
        .main-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
        }

        .main-header h1 {
            font-size: 2rem;
            font-weight: 700;
        }

        /* Contenedor de widgets con CSS Grid */
        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 1.5rem;
        }

        /* ------------------------------------------- */
        /*              Estilos de Widgets             */
        /* ------------------------------------------- */
        
        .widget {
            background-color: var(--color-surface);
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: var(--shadow);
            transition: all 0.3s ease;
            display: flex;
            flex-direction: column;
        }

        .widget:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        .widget-title {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 1rem;
            border-bottom: 1px solid var(--color-border);
            padding-bottom: 0.75rem;
            color: var(--color-text-primary);
        }
        
        /* Estilos específicos para cada widget */

        /* Widget de Saludo y Reloj */
        .widget-greeting {
            grid-column: 1 / -1; /* Ocupa todo el ancho */
            text-align: center;
            background: linear-gradient(135deg, var(--color-accent), var(--color-accent-hover));
            color: white;
        }

        #greeting {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        #clock {
            font-size: 4rem;
            font-weight: 200;
            letter-spacing: 2px;
            font-family: 'Courier New', Courier, monospace;
        }

        /* Widget de Lista de Tareas */
        /* En la sección de estilos CSS */

.theme-switcher {
    display: flex;
    align-items: center;
    gap: 10px; /* Espacio entre los iconos */
}

.theme-icon {
    font-size: 1.8rem; /* Ajusta el tamaño de los iconos */
    cursor: pointer;
    transition: transform 0.2s ease-in-out;
    padding: 5px; /* Añade un poco de padding para facilitar el clic */
    border-radius: 5px; /* Bordes ligeramente redondeados */
}

.theme-icon:hover {
    transform: scale(1.1); /* Efecto de escala al pasar el ratón */
}

/* Estilos para el icono activo (opcional, si quieres resaltarlo) */
body.light-mode #light-mode-toggle,
body.dark-mode #dark-mode-toggle {
    background-color: var(--color-accent); /* Color de fondo para el icono activo */
    color: white; /* Color del icono activo */
}

/* Elimina o comenta las siguientes reglas CSS antiguas */
/*
#theme-toggle:checked + .theme-switch {
    background-color: var(--color-accent);
}

#theme-toggle:checked + .theme-switch::before {
    transform: translateX(23px);
    background-color: white;
}

#theme-toggle {
    display: none; 
}
*/
#todo-form {
    display: flex;
    gap: 10px; /* Espacio entre el input y el botón */
    flex-wrap: wrap; /* Permite que los elementos se envuelvan en la siguiente línea si no hay espacio */
    align-items: center; /* Alinea verticalmente los elementos en el centro */
}

#todo-input {
    flex-grow: 1; /* Permite que el input ocupe el espacio disponible */
    min-width: 150px; /* Asegura que el input no sea demasiado pequeño */
    padding: 0.75rem;
    border: 1px solid var(--color-border);
    border-radius: 8px;
    background-color: var(--color-bg);
    color: var(--color-text-primary);
    font-size: 1rem;
    transition: border-color 0.3s ease;
    min-width: 150px; /* Asegura un ancho mínimo para el input */
}

#todo-input:focus {
    outline: none;
    border-color: var(--color-accent);
}

.todo-button {
    padding: 0.75rem 1rem;
    font-size: 1rem;
    font-weight: 600;
    background-color: var(--color-accent);
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    transition: background-color 0.3s ease;
    flex-shrink: 0; /* Evita que el botón se encoja */
}

.todo-button:hover {
    background-color: var(--color-accent-hover);
}

#todo-list {
    list-style: none;
    flex-grow: 1;
    overflow-y: auto;
    max-height: 300px;
}

/* Media query para pantallas más pequeñas si es necesario */
@media (max-width: 600px) {
    #todo-form {
        flex-direction: column; /* Apila los elementos verticalmente en pantallas pequeñas */
        align-items: stretch; /* Estira los elementos para que ocupen todo el ancho */
    }

    #todo-input,
    .todo-button {
        width: 100%; /* Ocupa todo el ancho disponible */
    }
}

.todo-button {
    flex-shrink: 0; /* Evita que el botón se encoja */
}

.todo-button:hover {
    background-color: var(--color-accent-hover);
}

#todo-list {
    list-style: none;
    flex-grow: 1;
    overflow-y: auto;
    max-height: 300px;
}

/* Scrollbar customizado */
        #todo-list::-webkit-scrollbar {
            width: 8px;
        }
        #todo-list::-webkit-scrollbar-track {
            background: var(--color-bg);
            border-radius: 4px;
        }
        #todo-list::-webkit-scrollbar-thumb {
            background: var(--color-border);
            border-radius: 4px;
        }
        #todo-list::-webkit-scrollbar-thumb:hover {
            background: var(--color-text-secondary);
        }

        .todo-item {
            display: flex;
            align-items: center;
            padding: 0.75rem 0.5rem;
            border-bottom: 1px solid var(--color-border);
            transition: background-color 0.2s ease;
        }

        .todo-item:last-child {
            border-bottom: none;
        }
        
        .todo-item:hover {
            background-color: var(--color-bg);
        }

        .todo-item input[type="checkbox"] {
            margin-right: 1rem;
            width: 20px;
            height: 20px;
            accent-color: var(--color-success);
            cursor: pointer;
        }

        .todo-item span {
            flex-grow: 1;
            color: var(--color-text-secondary);
            transition: all 0.3s ease;
        }
        
        .todo-item.completed span {
            text-decoration: line-through;
            color: var(--color-text-secondary);
            opacity: 0.6;
        }

        .delete-todo-btn {
            background: none;
            border: none;
            color: var(--color-danger);
            font-size: 1.2rem;
            cursor: pointer;
            opacity: 0;
            transition: opacity 0.3s ease, transform 0.2s ease;
        }

        .todo-item:hover .delete-todo-btn {
            opacity: 1;
        }

        .delete-todo-btn:hover {
            transform: scale(1.2);
        }

        /* Widget Temporizador Pomodoro */
        .widget-pomodoro {
            align-items: center;
        }

        #pomodoro-timer {
            font-size: 5rem;
            font-weight: 700;
            color: var(--color-accent);
            margin: 1rem 0;
            font-family: 'Courier New', Courier, monospace;
        }
        
        .pomodoro-modes {
            display: flex;
            gap: 0.5rem;
            margin-bottom: 1.5rem;
        }

        .pomodoro-mode-btn {
            padding: 0.5rem 1rem;
            border: 1px solid var(--color-border);
            border-radius: 20px;
            background-color: transparent;
            color: var(--color-text-secondary);
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .pomodoro-mode-btn.active {
            background-color: var(--color-accent);
            color: white;
            border-color: var(--color-accent);
        }

        .pomodoro-controls {
            display: flex;
            gap: 1rem;
        }

        .pomodoro-control-btn {
            padding: 0.75rem 1.5rem;
            font-size: 1rem;
            font-weight: 600;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: none;
        }
        
        #pomodoro-start-pause {
            background-color: var(--color-success);
            color: white;
        }

        #pomodoro-start-pause:hover {
            opacity: 0.9;
        }
        
        #pomodoro-reset {
            background-color: var(--color-danger);
            color: white;
        }
        
        #pomodoro-reset:hover {
            opacity: 0.9;
        }

        /* Widget de Notas */
        #notes-area {
            flex-grow: 1;
            width: 100%;
            padding: 1rem;
            border: 1px solid var(--color-border);
            border-radius: 8px;
            background-color: var(--color-bg);
            color: var(--color-text-primary);
            font-family: 'Menlo', 'Monaco', 'Courier New', monospace;
            font-size: 0.9rem;
            line-height: 1.5;
            resize: vertical;
            min-height: 200px;
            transition: border-color 0.3s ease;
        }

        #notes-area:focus {
            outline: none;
            border-color: var(--color-accent);
        }


        /* ------------------------------------------- */
        /*              Selector de Tema               */
        /* ------------------------------------------- */

        .theme-switcher {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .theme-switch-wrapper {
            position: relative;
        }
        
        .theme-switch {
            width: 50px;
            height: 26px;
            background-color: var(--color-surface);
            border: 1px solid var(--color-border);
            border-radius: 13px;
            cursor: pointer;
            position: relative;
            transition: background-color 0.3s ease;
        }
        
        .theme-switch::before {
            content: '';
            position: absolute;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            top: 2px;
            left: 3px;
            background-color: var(--color-text-secondary);
            transition: transform 0.3s ease;
        }

        #theme-toggle:checked + .theme-switch {
            background-color: var(--color-accent);
        }

        #theme-toggle:checked + .theme-switch::before {
            transform: translateX(23px);
            background-color: white;
        }

        #theme-toggle {
            display: none; /* Ocultamos el checkbox real */
        }

        /* ------------------------------------------- */
        /*              Diseño Responsivo              */
        /* ------------------------------------------- */
        
        /* Para tabletas y pantallas más pequeñas */
        @media (max-width: 992px) {
            .main-header h1 {
                font-size: 1.75rem;
            }

            #greeting {
                font-size: 2rem;
            }

            #clock {
                font-size: 3rem;
            }

            #pomodoro-timer {
                font-size: 4rem;
            }
        }

        /* Para móviles */
        @media (max-width: 768px) {
            .main-container {
                padding: 1rem;
            }

            .main-header {
                flex-direction: column;
                gap: 1rem;
                margin-bottom: 1.5rem;
            }

            .dashboard-grid {
                grid-template-columns: 1fr; /* Una sola columna en móviles */
                gap: 1rem;
            }
        }
        
        @media (max-width: 480px) {
             html {
                font-size: 14px;
            }
            
            .widget {
                padding: 1rem;
            }
            
            #greeting {
                font-size: 1.8rem;
            }

            #clock {
                font-size: 2.5rem;
            }
            
            #pomodoro-timer {
                font-size: 3.5rem;
            }
            
            .pomodoro-controls, .pomodoro-modes {
                flex-wrap: wrap;
                justify-content: center;
            }
            
            #todo-form {
                flex-direction: column;
                width: 100%; /* Ocupa todo el ancho disponible */
            }

            #todo-input {
                width: 100%; /* El input ocupa todo el ancho en columna */
                margin-bottom: 0.5rem; /* Espacio entre el input y el botón */
            }

            .todo-button {
                width: 100%; /* El botón ocupa todo el ancho en columna */
            }
        }

    </style>
</head>
<body class="">

    <!-- 
    =====================================================================
    HTML (LENGUAJE DE MARCADO DE HIPERTEXTO)
    Esta es la estructura y el contenido de la página.
    =====================================================================
    -->

    <div class="main-container">

        <!-- Cabecera con título y selector de tema -->
        <header class="main-header">
            <h1>Mi Dashboard de Productividad</h1>
            <div class="theme-switcher">
                <span id="light-mode-toggle" class="theme-icon">☀️</span>
                <span id="dark-mode-toggle" class="theme-icon">🌙</span>
            </div>
        </header>

        <!-- Rejilla principal para los widgets -->
        <main class="dashboard-grid">

            <!-- Widget: Saludo y Reloj -->
            <section class="widget widget-greeting">
                <h2 id="greeting"></h2>
                <p id="clock"></p>
            </section>

            <!-- Widget: Temporizador Pomodoro -->
            <section class="widget widget-pomodoro">
                <h3 class="widget-title">Temporizador Pomodoro</h3>
                <div class="pomodoro-modes">
                    <button class="pomodoro-mode-btn active" data-mode="pomodoro">Pomodoro</button>
                    <button class="pomodoro-mode-btn" data-mode="shortBreak">Descanso Corto</button>
                    <button class="pomodoro-mode-btn" data-mode="longBreak">Descanso Largo</button>
                </div>
                <div id="pomodoro-timer">25:00</div>
                <div class="pomodoro-controls">
                    <button id="pomodoro-start-pause" class="pomodoro-control-btn">Iniciar</button>
                    <button id="pomodoro-reset" class="pomodoro-control-btn">Reiniciar</button>
                </div>
            </section>

            <!-- Widget: Lista de Tareas (To-Do List) -->
            <section class="widget">
                <h3 class="widget-title">Lista de Tareas</h3>
                <form id="todo-form">
                    <input type="text" id="todo-input" placeholder="Añadir nueva tarea..." autocomplete="off">
                    <button type="submit" class="todo-button">Añadir</button>
                </form>
                <ul id="todo-list">
                    <!-- Las tareas se generarán aquí con JavaScript -->
                </ul>
            </section>
            
            <!-- Widget: Bloc de Notas -->
            <section class="widget">
                <h3 class="widget-title">Bloc de Notas Rápido</h3>
                <textarea id="notes-area" placeholder="Escribe tus pensamientos, ideas o notas rápidas aquí... Los cambios se guardan automáticamente."></textarea>
            </section>

        </main>

    </div>

    <!-- 
    =====================================================================
    JAVASCRIPT
    Toda la lógica e interactividad de la página se encuentra aquí.
    =====================================================================
    -->
    <script>

    // Se espera a que todo el contenido del DOM (la estructura HTML) esté cargado
    // antes de ejecutar cualquier script para evitar errores.
    document.addEventListener('DOMContentLoaded', () => {

        // ------------------------------------------- //
        //          SELECCIÓN DE ELEMENTOS DEL DOM       //
        // ------------------------------------------- //
        // Guardamos en constantes las referencias a los elementos HTML con los que vamos a interactuar.
        
        // Elementos generales
        const body = document.body;

        // Elementos del Saludo y Reloj
        const greetingEl = document.getElementById('greeting');
        const clockEl = document.getElementById('clock');
        
        // Elementos del Selector de Tema
        const lightModeToggle = document.getElementById('light-mode-toggle');
        const darkModeToggle = document.getElementById('dark-mode-toggle');

        // Elementos de la Lista de Tareas
        const todoForm = document.getElementById('todo-form');
        const todoInput = document.getElementById('todo-input');
        const todoList = document.getElementById('todo-list');

        // Elementos del Temporizador Pomodoro
        const pomodoroTimerEl = document.getElementById('pomodoro-timer');
        const pomodoroStartPauseBtn = document.getElementById('pomodoro-start-pause');
        const pomodoroResetBtn = document.getElementById('pomodoro-reset');
        const pomodoroModeBtns = document.querySelectorAll('.pomodoro-mode-btn');

        // Elementos del Bloc de Notas
        const notesArea = document.getElementById('notes-area');


        // ------------------------------------------- //
        //                  ESTADO GLOBAL                //
        // ------------------------------------------- //
        // Un objeto para mantener el estado de la aplicación, como la lista de tareas.
        // Esto ayuda a organizar y centralizar los datos.
        
        let todos = []; // Array para almacenar las tareas

        // Estado del temporizador Pomodoro
        const pomodoroState = {
            timerId: null,
            timeRemaining: 25 * 60, // en segundos
            isPaused: true,
            mode: 'pomodoro', // 'pomodoro', 'shortBreak', 'longBreak'
            durations: {
                pomodoro: 25 * 60,
                shortBreak: 5 * 60,
                longBreak: 15 * 60
            }
        };


        // ------------------------------------------- //
        //            LÓGICA DE LOS WIDGETS              //
        // ------------------------------------------- //

        // --- Widget: Reloj y Saludo --- //

        function updateClockAndGreeting() {
            const now = new Date();
            
            // Actualizar reloj
            const hours = String(now.getHours()).padStart(2, '0');
            const minutes = String(now.getMinutes()).padStart(2, '0');
            const seconds = String(now.getSeconds()).padStart(2, '0');
            clockEl.textContent = `${hours}:${minutes}:${seconds}`;

            // Actualizar saludo
            const currentHour = now.getHours();
            if (currentHour < 12) {
                greetingEl.textContent = 'Buenos días';
            } else if (currentHour < 19) {
                greetingEl.textContent = 'Buenas tardes';
            } else {
                greetingEl.textContent = 'Buenas noches';
            }
        }

        // --- Widget: Selector de Tema --- //
        
        function applyTheme(isDarkMode) {
            if (isDarkMode) {
                body.classList.add('dark-mode');
                body.classList.remove('light-mode'); // Asegurarse de remover la clase light-mode
            } else {
                body.classList.remove('dark-mode');
                body.classList.add('light-mode'); // Asegurarse de añadir la clase light-mode
            }
            // Opcional: Actualizar el estado visual de los iconos si no se hace con CSS
            // lightModeToggle.classList.toggle('active', !isDarkMode);
            // darkModeToggle.classList.toggle('active', isDarkMode);
        }

        function toggleTheme() {
            const isDarkMode = themeToggle.checked;
            applyTheme(isDarkMode);
            // Guardar la preferencia en localStorage
            localStorage.setItem('dashboardTheme', isDarkMode ? 'dark' : 'light');
        }

        function loadTheme() {
            const savedTheme = localStorage.getItem('dashboardTheme');
            // Si no hay tema guardado, usar la preferencia del sistema operativo
            if (savedTheme === null) {
                if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
                    applyTheme(true);
                } else {
                    applyTheme(false);
                }
            } else {
                applyTheme(savedTheme === 'dark');
            }
        }
        
        // --- Widget: Lista de Tareas (To-Do List) --- //

        function renderTodos() {
            todoList.innerHTML = ''; // Limpiar la lista actual para re-renderizar

            if (todos.length === 0) {
                const emptyMessage = document.createElement('li');
                emptyMessage.textContent = '¡Todo listo! No hay tareas pendientes.';
                emptyMessage.style.color = 'var(--color-text-secondary)';
                emptyMessage.style.textAlign = 'center';
                emptyMessage.style.padding = '1rem 0';
                todoList.appendChild(emptyMessage);
                return;
            }

            todos.forEach(todo => {
                const li = document.createElement('li');
                li.classList.add('todo-item');
                li.dataset.id = todo.id;

                if (todo.completed) {
                    li.classList.add('completed');
                }

                const checkbox = document.createElement('input');
                checkbox.type = 'checkbox';
                checkbox.checked = todo.completed;

                const span = document.createElement('span');
                span.textContent = todo.text;

                const deleteBtn = document.createElement('button');
                deleteBtn.classList.add('delete-todo-btn');
                deleteBtn.innerHTML = '×'; // Símbolo de 'x'

                li.appendChild(checkbox);
                li.appendChild(span);
                li.appendChild(deleteBtn);

                todoList.appendChild(li);
            });
        }
        
        function addTodo(e) {
            e.preventDefault(); // Evitar que el formulario recargue la página
            const text = todoInput.value.trim();
            if (text) {
                const newTodo = {
                    id: Date.now(), // ID único basado en el tiempo
                    text: text,
                    completed: false
                };
                todos.push(newTodo);
                todoInput.value = '';
                saveAndRenderTodos();
            }
        }

        function handleTodoListClick(e) {
            const target = e.target;
            const parentLi = target.closest('.todo-item');
            if (!parentLi) return;
            
            const todoId = Number(parentLi.dataset.id);

            // Si se hizo clic en el checkbox
            if (target.type === 'checkbox') {
                toggleTodo(todoId);
            }

            // Si se hizo clic en el botón de borrar
            if (target.classList.contains('delete-todo-btn')) {
                deleteTodo(todoId);
            }
        }

        function toggleTodo(id) {
            todos = todos.map(todo => 
                todo.id === id ? { ...todo, completed: !todo.completed } : todo
            );
            saveAndRenderTodos();
        }

        function deleteTodo(id) {
            todos = todos.filter(todo => todo.id !== id);
            saveAndRenderTodos();
        }

        function saveAndRenderTodos() {
            saveTodos();
            renderTodos();
        }

        function saveTodos() {
            localStorage.setItem('dashboardTodos', JSON.stringify(todos));
        }

        // Cargar tareas guardadas al inicio
        loadTodos();

        function loadTodos() {
            const savedTodos = localStorage.getItem('dashboardTodos');
            if (savedTodos) {
                todos = JSON.parse(savedTodos);
            }
            renderTodos();
        }

        // --- Widget: Temporizador Pomodoro --- //
        
        function updatePomodoroDisplay() {
            const minutes = Math.floor(pomodoroState.timeRemaining / 60);
            const seconds = pomodoroState.timeRemaining % 60;
            pomodoroTimerEl.textContent = `${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`;
            document.title = `${pomodoroTimerEl.textContent} - ${pomodoroState.mode.charAt(0).toUpperCase() + pomodoroState.mode.slice(1)}`;
        }

        function startPausePomodoro() {
            pomodoroState.isPaused = !pomodoroState.isPaused;
            if (!pomodoroState.isPaused) {
                pomodoroStartPauseBtn.textContent = 'Pausar';
                pomodoroState.timerId = setInterval(() => {
                    pomodoroState.timeRemaining--;
                    updatePomodoroDisplay();
                    if (pomodoroState.timeRemaining <= 0) {
                        clearInterval(pomodoroState.timerId);
                        alarmSound.play();
                        // Cambiar al siguiente modo automáticamente
                        switch (pomodoroState.mode) {
                            case 'pomodoro':
                                switchMode('shortBreak');
                                break;
                            case 'shortBreak':
                                switchMode('pomodoro');
                                break;
                            case 'longBreak':
                                switchMode('pomodoro');
                                break;
                        }
                    }
                }, 1000);
            } else {
                pomodoroStartPauseBtn.textContent = 'Reanudar';
                clearInterval(pomodoroState.timerId);
            }
        }

        function resetPomodoro() {
            clearInterval(pomodoroState.timerId);
            pomodoroState.isPaused = true;
            pomodoroStartPauseBtn.textContent = 'Iniciar';
            pomodoroState.timeRemaining = pomodoroState.durations[pomodoroState.mode];
            updatePomodoroDisplay();
            document.title = 'Dashboard de Productividad';
        }

        function switchMode(newMode) {
            pomodoroState.mode = newMode;
            
            // Actualizar la clase 'active' en los botones de modo
            pomodoroModeBtns.forEach(btn => {
                btn.classList.toggle('active', btn.dataset.mode === newMode);
            });
            
            resetPomodoro();
        }

        // --- Widget: Bloc de Notas --- //
        
        function saveNotes() {
            localStorage.setItem('dashboardNotes', notesArea.value);
        }

        function loadNotes() {
            const savedNotes = localStorage.getItem('dashboardNotes');
            if (savedNotes) {
                notesArea.value = savedNotes;
            }
        }


        // ------------------------------------------- //
        //            ASIGNACIÓN DE EVENTOS            //
        // ------------------------------------------- //
        // Aquí se conectan las funciones con las acciones del usuario (clics, envíos, etc.).

        // Event Listeners para los nuevos botones de tema
        lightModeToggle.addEventListener('click', () => {
            applyTheme(false); // Aplicar tema claro
            localStorage.setItem('dashboardTheme', 'light');
        });

        darkModeToggle.addEventListener('click', () => {
            applyTheme(true); // Aplicar tema oscuro
            localStorage.setItem('dashboardTheme', 'dark');
        });

        // Cargar el tema al inicio
        loadTheme();

        // Eventos para la lista de tareas
        todoForm.addEventListener('submit', addTodo);
        todoList.addEventListener('click', handleTodoListClick);

        // Eventos para el temporizador Pomodoro
        pomodoroStartPauseBtn.addEventListener('click', startPausePomodoro);
        pomodoroResetBtn.addEventListener('click', resetPomodoro);
        pomodoroModeBtns.forEach(btn => {
            btn.addEventListener('click', () => switchMode(btn.dataset.mode));
        });

        // Evento para el bloc de notas (guardado automático)
        notesArea.addEventListener('input', saveNotes);


        // ------------------------------------------- //
        //                 INICIALIZACIÓN                //
        // ------------------------------------------- //
        // Funciones que se ejecutan una vez al cargar la página.
        
        function init() {
            // Cargar datos guardados
            loadTodos();
            loadNotes();

            // Iniciar el reloj y el saludo
            updateClockAndGreeting();
            setInterval(updateClockAndGreeting, 1000); // Actualizar cada segundo

            // Establecer el estado inicial del temporizador
            resetPomodoro();
        }

        // ¡Empezar todo!
        init();

    });

    </script>
</body>
</html>