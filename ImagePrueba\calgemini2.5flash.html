<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Calculadora Avanzada</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            background-color: #e0e0e0;
            margin: 0;
            padding: 20px;
            box-sizing: border-box;
        }

        .calculator-container {
            background-color: #333;
            border-radius: 15px;
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
            overflow: hidden;
            width: 350px;
            max-width: 100%;
            padding: 20px;
            display: flex;
            flex-direction: column;
        }

        .display {
            background-color: #222;
            color: #0f0;
            font-size: 2.5em;
            text-align: right;
            padding: 20px;
            margin-bottom: 20px;
            border-radius: 10px;
            overflow: hidden;
            word-wrap: break-word;
            min-height: 1.5em;
            display: flex;
            flex-direction: column;
            justify-content: space-around;
        }

        .display .history {
            font-size: 0.5em;
            color: #aaa;
        }

        .display .current {
            font-size: 1em;
            color: #fff;
        }

        .buttons {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            grid-gap: 10px;
        }

        .button {
            background-color: #555;
            color: #fff;
            font-size: 1.3em;
            padding: 20px 0;
            text-align: center;
            cursor: pointer;
            transition: background-color 0.2s ease, transform 0.1s ease, box-shadow 0.2s ease;
            border-radius: 8px;
            user-select: none;
            border: none;
            outline: none;
        }

        .button:hover {
            background-color: #777;
            box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
        }

        .button:active {
            background-color: #999;
            transform: scale(0.97);
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
        }

        .operator {
            background-color: #f90;
        }

        .operator:hover {
            background-color: #ffb14a;
        }

        .operator:active {
            background-color: #ffc87a;
        }

        .equals {
            background-color: #0c0;
            grid-column: span 2;
        }

        .equals:hover {
            background-color: #2edb2e;
        }

        .equals:active {
            background-color: #5cff5c;
        }

        .clear {
            background-color: #c00;
        }

        .clear:hover {
            background-color: #ff3333;
        }

        .clear:active {
            background-color: #ff6666;
        }

        .function-btn {
            background-color: #444;
            font-size: 1em;
        }

        .function-btn:hover {
             background-color: #666;
        }

        .function-btn:active {
             background-color: #888;
        }

        /* Responsiveness */
        @media (max-width: 450px) {
            .calculator-container {
                width: 100%;
                padding: 10px;
            }

            .buttons {
                grid-gap: 5px;
            }

            .button {
                padding: 15px 0;
                font-size: 1.1em;
            }

            .display {
                font-size: 2em;
                padding: 15px;
                margin-bottom: 10px;
            }
        }

        /* Animation example */
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        .calculator-container {
            animation: fadeIn 0.5s ease-out;
        }

    </style>
</head>
<body>

    <div class="calculator-container">
        <div class="display">
            <div class="history" id="history"></div>
            <div class="current" id="current-display">0</div>
        </div>
        <div class="buttons">
            <button class="button clear">C</button>
            <button class="button function-btn">√</button>
            <button class="button function-btn">x²</button>
            <button class="button operator">/</button>

            <button class="button">7</button>
            <button class="button">8</button>
            <button class="button">9</button>
            <button class="button operator">*</button>

            <button class="button">4</button>
            <button class="button">5</button>
            <button class="button">6</button>
            <button class="button operator">-</button>

            <button class="button">1</button>
            <button class="button">2</button>
            <button class="button">3</button>
            <button class="button operator">+</button>

            <button class="button function-btn">+/-</button>
            <button class="button">0</button>
            <button class="button">.</button>
            <button class="button equals">=</button>
        </div>
    </div>

    <script>
        const currentDisplay = document.getElementById('current-display');
        const historyDisplay = document.getElementById('history');
        const buttons = document.querySelectorAll('.button');

        let currentInput = '0';
        let operator = null;
        let previousInput = null;
        let history = '';
        let waitingForSecondOperand = false;

        function updateDisplay() {
            currentDisplay.innerText = currentInput;
            historyDisplay.innerText = history;
        }

        function resetCalculator() {
            currentInput = '0';
            operator = null;
            previousInput = null;
            history = '';
            waitingForSecondOperand = false;
        }

        function performOperation(num1, num2, operator) {
            switch (operator) {
                case '+': return num1 + num2;
                case '-': return num1 - num2;
                case '*': return num1 * num2;
                case '/': return num1 / num2;
                case '√': return Math.sqrt(num2); // Note: √ operates on the current input
                case 'x²': return Math.pow(num2, 2); // Note: x² operates on the current input
                default: return num2;
            }
        }

        buttons.forEach(button => {
            button.addEventListener('click', (e) => {
                const value = e.target.innerText;

                // Add pulsing animation
                e.target.classList.add('pulsing');
                e.target.addEventListener('animationend', () => {
                    e.target.classList.remove('pulsing');
                }, { once: true });

                if (e.target.classList.contains('clear')) {
                    resetCalculator();
                } else if (e.target.classList.contains('operator')) {
                    if (operator && waitingForSecondOperand) {
                         operator = value;
                         history = `${previousInput} ${operator}`;
                         updateDisplay();
                         return;
                    }

                    if (previousInput !== null) {
                         const result = performOperation(previousInput, parseFloat(currentInput), operator);
                         currentInput = result.toString();
                         history = `${previousInput} ${operator} ${parseFloat(currentInput)} = ${currentInput}`;
                         previousInput = result;
                    } else {
                         previousInput = parseFloat(currentInput);
                         history = `${previousInput}`;
                    }

                    operator = value;
                    history = `${previousInput} ${operator}`;
                    currentInput = '0';
                    waitingForSecondOperand = true;

                } else if (e.target.classList.contains('equals')) {
                    if (operator && previousInput !== null) {
                        const secondOperand = parseFloat(currentInput);
                        const result = performOperation(previousInput, secondOperand, operator);
                        history = `${previousInput} ${operator} ${secondOperand} = ${result}`;
                        currentInput = result.toString();
                        operator = null;
                        previousInput = null;
                        waitingForSecondOperand = false;
                    }
                } else if (e.target.classList.contains('function-btn')) {
                     if (value === '√') {
                         const num = parseFloat(currentInput);
                         if (!isNaN(num) && num >= 0) {
                             const result = Math.sqrt(num);
                             history = `√(${currentInput}) = ${result}`;
                             currentInput = result.toString();
                         } else {
                             currentInput = 'Error';
                             history = '';
                         }
                     } else if (value === 'x²') {
                         const num = parseFloat(currentInput);
                         if (!isNaN(num)) {
                             const result = Math.pow(num, 2);
                             history = `${currentInput}² = ${result}`;
                             currentInput = result.toString();
                         } else {
                             currentInput = 'Error';
                             history = '';
                         }
                     } else if (value === '+/-') {
                         const num = parseFloat(currentInput);
                         if (!isNaN(num)) {
                             currentInput = (num * -1).toString();
                         }
                     }
                     waitingForSecondOperand = false;
                } else {
                    if (currentInput === '0' || waitingForSecondOperand) {
                        currentInput = value;
                        waitingForSecondOperand = false;
                    } else {
                        currentInput += value;
                    }
                }

                updateDisplay();
            });
        });

        updateDisplay(); // Initialize display

    </script>

</body>
</html>