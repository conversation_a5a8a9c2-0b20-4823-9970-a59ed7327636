<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tanuki en HTML y CSS</title>
    <style>
        body {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            background-color: #cffafe; /* Similar al fondo de la imagen */
            margin: 0;
            font-family: sans-serif;
        }

        .tanuki-container {
            position: relative;
            width: 300px;
            height: 400px;
        }

        /* Estilo base para las partes del cuerpo con el efecto "sketch" */
        .tanuki-part {
            position: absolute;
            border: 3px solid #333; /* Borde oscuro para el efecto sketch */
            box-sizing: border-box;
        }

        .head {
            width: 150px;
            height: 130px;
            background-color: #d2a679; /* Color principal del tanuki */
            border-radius: 50% 50% 45% 45%;
            top: 50px;
            left: 75px;
            z-index: 2;
        }

        .ear {
            width: 50px;
            height: 55px;
            background-color: #d2a679;
            border-radius: 50% 50% 30% 30% / 60% 60% 40% 40%;
            z-index: 1;
        }

        .ear-left {
            top: 25px;
            left: 70px;
            transform: rotate(-15deg);
        }

        .ear-right {
            top: 25px;
            left: 180px;
            transform: rotate(15deg);
        }

        .inner-ear {
            position: absolute;
            width: 30px;
            height: 35px;
            background-color: #b88a68; /* Sombra interior oreja */
            border-radius: 50%;
            top: 8px;
            left: 8px;
            border: 2px solid #333;
        }

        .face-mask {
            width: 130px;
            height: 70px;
            background-color: #f5e5d5; /* Mascara más clara */
            border-radius: 45% 45% 50% 50%;
            top: 40px; /* Ajustar para que quede dentro de la cabeza */
            left: 10px; /* Ajustar para que quede dentro de la cabeza */
            z-index: 3;
            border: none; /* La máscara no tiene borde propio, se superpone */
        }
        
        .head .face-mask { /* Para que la máscara esté contenida en la cabeza */
             position: absolute;
        }


        .eye {
            width: 18px;
            height: 22px;
            background-color: #333; /* Ojos negros */
            border-radius: 50%;
            z-index: 4;
        }

        .eye-left {
            top: 65px;
            left: 45px; /* Dentro de la máscara */
        }

        .eye-right {
            top: 65px;
            left: 85px; /* Dentro de la máscara */
        }
        
        .eye-highlight {
            position: absolute;
            width: 5px;
            height: 5px;
            background-color: white;
            border-radius: 50%;
            top: 5px;
            left: 4px;
        }

        .nose {
            width: 20px;
            height: 15px;
            background-color: #333;
            border-radius: 40% 40% 50% 50% / 60% 60% 50% 50%;
            top: 90px;
            left: 65px;
            z-index: 4;
        }

        .mouth {
            width: 25px;
            height: 15px;
            border-bottom: 3px solid #333;
            border-left: 3px solid transparent; /* Para crear la curva */
            border-right: 3px solid transparent; /* Para crear la curva */
            border-radius: 0 0 50% 50% / 0 0 100% 100%;
            top: 105px;
            left: 62px;
            z-index: 4;
            background-color: transparent; /* Sin fondo */
        }

        .body {
            width: 180px;
            height: 200px;
            background-color: #d2a679;
            border-radius: 50% 50% 45% 45% / 60% 60% 40% 40%;
            top: 150px;
            left: 60px;
            z-index: 1;
        }

        .belly-patch {
            width: 90px;
            height: 100px;
            background-color: #f5e5d5; /* Color crema para la panza */
            border-radius: 50%;
            top: 50px; /* Relativo al cuerpo */
            left: 45px; /* Relativo al cuerpo */
            z-index: 2;
        }
        
        .body .belly-patch { /* Para que esté contenido en el cuerpo */
            position: absolute;
        }

        .belly-x {
            z-index: 3;
            top: 90px; /* Relativo al cuerpo, centrado en la panza */
            left: 82px; /* Relativo al cuerpo */
            width: 15px;
            height: 15px;
            border: none; /* Sin borde propio */
        }
        .body .belly-x {
            position: absolute;
        }

        .belly-x::before,
        .belly-x::after {
            content: '';
            position: absolute;
            width: 20px;
            height: 4px;
            background-color: #333;
            border-radius: 2px;
            top: 50%;
            left: 50%;
        }

        .belly-x::before {
            transform: translate(-50%, -50%) rotate(45deg);
        }

        .belly-x::after {
            transform: translate(-50%, -50%) rotate(-45deg);
        }

        .arm {
            width: 50px;
            height: 100px;
            background-color: #d2a679;
            border-radius: 25px 25px 20px 20px; /* Más redondeado en un extremo */
            z-index: 3; /* Por encima del cuerpo */
        }

        .arm-left { /* Brazo derecho del tanuki, a nuestra izquierda */
            top: 170px;
            left: 30px;
            transform: rotate(30deg);
            transform-origin: top right;
        }

        .arm-right { /* Brazo izquierdo del tanuki, a nuestra derecha (bajado) */
            top: 170px;
            left: 190px; /* Ajustar posición */
            transform: rotate(-20deg);
            transform-origin: top left;
            z-index: 0; /* Detrás del cuerpo */
        }
        
        .leg {
            width: 60px;
            height: 70px;
            background-color: #d2a679;
            border-radius: 30px 30px 20px 20px / 50% 50% 30% 30%;
            z-index: 0; /* Detrás del cuerpo */
        }

        .leg-left {
            top: 310px;
            left: 80px;
        }

        .leg-right {
            top: 310px;
            left: 160px;
        }

        .tail {
            width: 80px;
            height: 130px;
            background-color: #b88a68; /* Color base de la cola */
            border-radius: 40px 40px 30px 60px / 60px 60px 40px 80px;
            top: 220px;
            left: 200px; /* A la derecha del cuerpo */
            z-index: 0; /* Detrás del cuerpo */
            transform: rotate(15deg);
        }

        .tail-stripe {
            position: absolute;
            width: 100%; /* Ancho completo de la cola */
            height: 25px;
            background-color: #8c6c4e; /* Color más oscuro para las rayas */
            border: 2px solid #333; /* Borde también para las rayas */
            box-sizing: border-box;
        }

        .tail .stripe1 {
            top: 20px;
            left: 0;
            border-radius: 15px 15px 5px 5px / 20px 20px 5px 5px;
            height: 30px;
        }

        .tail .stripe2 {
            top: 60px;
            left: 0;
            border-radius: 10px;
            height: 28px;
        }
        .tail .stripe3 {
            top: 95px;
            left: 0;
            border-radius: 10px 10px 20px 20px / 10px 10px 30px 30px;
            height: 25px;
        }


    </style>
</head>
<body>
    <div class="tanuki-container">
        <!-- Cabeza y componentes faciales -->
        <div class="tanuki-part ear ear-left">
            <div class="inner-ear"></div>
        </div>
        <div class="tanuki-part ear ear-right">
            <div class="inner-ear"></div>
        </div>
        <div class="tanuki-part head">
            <div class="face-mask"></div> <!-- No es tanuki-part para evitar doble borde -->
            <div class="tanuki-part eye eye-left">
                 <div class="eye-highlight"></div>
            </div>
            <div class="tanuki-part eye eye-right">
                <div class="eye-highlight"></div>
            </div>
            <div class="tanuki-part nose"></div>
            <div class="tanuki-part mouth"></div>
        </div>

        <!-- Cuerpo y extremidades -->
        <div class="tanuki-part body">
            <div class="tanuki-part belly-patch"></div>
            <div class="belly-x"></div> <!-- No es tanuki-part, se crea con pseudo-elementos -->
        </div>
        <div class="tanuki-part arm arm-left"></div>
        <div class="tanuki-part arm arm-right"></div>
        <div class="tanuki-part leg leg-left"></div>
        <div class="tanuki-part leg leg-right"></div>
        
        <!-- Cola -->
        <div class="tanuki-part tail">
            <div class="tail-stripe stripe1"></div>
            <div class="tail-stripe stripe2"></div>
            <div class="tail-stripe stripe3"></div>
        </div>
    </div>
</body>
</html>