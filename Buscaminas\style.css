:root {
    --cell-size: 36px;
    --cell-gap: 4px;
    --container-bg: rgba(255,255,255,0.08); /* Ligeramente más opaco */
    --container-radius: 20px; /* Un poco más redondeado */
    --container-shadow: 0 10px 35px 0 rgba(0,0,0,0.25); /* Sombra más suave y difusa */
    --primary-font: 'Segoe UI', Arial, sans-serif;
    --main-bg: linear-gradient(135deg, #232526 0%, #414345 100%);
    --mine-bg: #ff5e62;
    --mine-color: #fff;
    --flag-bg: #ffc107; /* Amarillo más vibrante para banderas */
    --flag-color: #5d4037; /* Marrón oscuro para el ícono de bandera */
    --revealed-cell-bg: #e0e0e0; /* Fondo más claro para celdas reveladas */
    --cell-hover-bg: rgba(255,255,255,0.25);
}

body {
    background: var(--main-bg);
    min-height: 100vh;
    margin: 0;
    font-family: var(--primary-font);
    display: flex;
    align-items: center;
    justify-content: center;
}

.container {
    background: var(--container-bg);
    padding: 2rem 2.5rem 2.5rem 2.5rem;
    border-radius: var(--container-radius);
    box-shadow: var(--container-shadow);
    text-align: center;
}

h1 {
    color: #fff;
    margin-bottom: 1rem; /* Más espacio */
    letter-spacing: 3px; /* Un poco más de espaciado */
    font-weight: 700;
    font-size: 2.5em; /* Más grande */
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.controls {
    margin-bottom: 1.5rem; /* Más espacio */
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 1.1rem;
}

#restart {
    background: #4CAF50; /* Un color verde más estándar para acción positiva */
    color: #fff;
    border: none;
    padding: 0.6rem 1.2rem;
    border-radius: 8px;
    font-size: 1rem;
    cursor: pointer;
    transition: background 0.2s, transform 0.1s;
    font-weight: 600;
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
}
#restart:hover {
    background: #45a049; /* Verde más oscuro al pasar el mouse */
    transform: translateY(-1px);
}
#restart:active {
    transform: translateY(0px);
    box-shadow: 0 1px 3px rgba(0,0,0,0.2);
}

#mines-count, #best-time {
    color: #fff;
    font-size: 1.1rem;
    font-weight: 500;
}

#board {
    display: grid;
    grid-template-columns: repeat(10, var(--cell-size));
    grid-template-rows: repeat(10, var(--cell-size));
    gap: var(--cell-gap);
    justify-content: center;
    margin-top: 0.6rem;
}

.cell {
    width: var(--cell-size);
    height: var(--cell-size);
    background: rgba(255,255,255,0.15);
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    color: #222;
    cursor: pointer;
    user-select: none;
    box-shadow: 0 2px 8px rgba(0,0,0,0.08);
    transition: background 0.15s, box-shadow 0.15s, transform 0.2s;
    font-weight: 600;
}

.cell:not(.revealed):hover {
    background: var(--cell-hover-bg);
    transform: translateY(-2px);
    box-shadow: 0 4px 10px rgba(0,0,0,0.12);
}

.cell.revealed {
    background: var(--revealed-cell-bg); /* Fondo más claro */
    cursor: default;
    box-shadow: inset 1px 1px 3px rgba(0,0,0,0.1); /* Sombra interior sutil */
    animation: pop 0.2s;
}

.cell.mine {
    background: var(--mine-bg);
    color: var(--mine-color);
    animation: shake 0.4s;
    /* Podrías añadir un ícono de bomba aquí, ej: content: '💣'; */
}

@keyframes pop {
    0% { transform: scale(1.2);}
    100% { transform: scale(1);}
}

@keyframes shake {
    0% { transform: translateX(0);}
    20% { transform: translateX(-5px);}
    40% { transform: translateX(5px);}
    60% { transform: translateX(-5px);}
    80% { transform: translateX(5px);}
    100% { transform: translateX(0);}
}

.cell.flagged {
    background: var(--flag-bg);
    color: var(--flag-color); /* Color para el texto si lo hubiera, aunque lo limpiaremos */
    /* No establecer content aquí directamente */
}

.cell.flagged::before { 
    content: '🚩'; 
    font-size: 1.2rem; /* Ajustar tamaño del emoji si es necesario */
    line-height: 1; /* Asegurar alineación vertical */
    color: var(--flag-color); /* Color del emoji de bandera */
}

.cell.mine {
    background: var(--mine-bg);
    color: var(--mine-color);
    animation: shake 0.4s;
    /* El textContent '💣' se establece desde JS */
}

.cell.mine::before {
    content: ''; /* Asegurar que no haya pseudo-elemento para minas si se usa textContent */
}

.cell[data-number="1"] { color: #1976d2; }
.cell[data-number="2"] { color: #388e3c; }
.cell[data-number="3"] { color: #d32f2f; }
.cell[data-number="4"] { color: #7b1fa2; }
.cell[data-number="5"] { color: #ff8f00; }
.cell[data-number="6"] { color: #00897b; }
.cell[data-number="7"] { color: #455a64; }
.cell[data-number="8"] { color: #c2185b; }