<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>¡<PERSON><PERSON>z Cumpleaños Wawita!</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Pacifico&family=Poppins:wght@300;400;600&display=swap');
        :root {
            --primary-color: #ff69b4;
            --secondary-color: #ffc0cb;
            --accent-color: #87ceeb;
            --text-color: #333;
            --light-text-color: #555;
            --bg-color: #fff0f5;
            --dark-bg-color: #2c2c2c;
            --envelope-color: #f0e68c;
            --envelope-flap-color: #e0d682;
            --cat-main-color: #78909c;
            --cat-accent-color: #b0bec5;
            --cat-nose-color: #ff80ab;
            --dog-main-color: #bcaaa4;
            --dog-accent-color: #d7ccc8;
            --dog-nose-color: #5d4037;
            --eye-color: #263238;
            --pupil-color: #000000;
            --modal-bg: rgba(0,0,0,0.6);
        }
        body {
            margin: 0; font-family: 'Poppins', sans-serif; background-color: var(--dark-bg-color);
            display: flex; justify-content: center; align-items: center;
            min-height: 100vh; overflow: hidden;
            perspective: 1200px;
            transition: background-color 0.5s ease-in-out;
        }
        body.card-visible {
            background-color: var(--bg-color);
        }

        #initialEnvelope { display: none; position: relative; width: 200px; height: 120px; background-color: var(--envelope-color); border-radius: 8px; box-shadow: 0 5px 15px rgba(0,0,0,0.2); cursor: pointer; transition: transform 0.3s ease, box-shadow 0.3s ease; }
        #initialEnvelope:hover { transform: translateY(-5px) scale(1.05); box-shadow: 0 8px 20px rgba(0,0,0,0.25); }
        #initialEnvelope::before { content: ''; position: absolute; top: 0; left: 0; width: 100%; height: 60px; background-color: var(--envelope-flap-color); clip-path: polygon(0 0, 100% 0, 50% 100%); border-radius: 8px 8px 0 0; }
        #initialEnvelope::after { content: '💌'; position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%) scale(1.8); font-size: 20px; opacity: 0.6; }

        #preloader { display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; justify-content: center; align-items: center; z-index: 1000; opacity: 0; transition: opacity 0.7s ease-out 0.3s; }
        #preloader.visible { display: flex; opacity: 1; }
        #preloader.hidden { opacity: 0; pointer-events: none; }

        .beating-heart-bg { position: absolute; top: 50%; left: 50%; width: 180px; height: 160px; transform: translate(-50%, -50%); opacity: 0; z-index: -1; animation: beat 1.5s infinite ease-in-out; }
        .beating-heart-bg::before, .beating-heart-bg::after { content: ""; position: absolute; top: 0; width: 94px; height: 144px; border-radius: 50px 50px 0 0; background: transparent; box-shadow: 0 0 3px var(--primary-color), 0 0 6px var(--primary-color), 0 0 10px var(--primary-color), 0 0 15px var(--primary-color), 0 0 20px var(--primary-color); }
        .beating-heart-bg::before { left: 90px; transform: rotate(-45deg); transform-origin: 0 100%; }
        .beating-heart-bg::after { left: 0; transform: rotate(45deg); transform-origin: 100% 100%; }
        @keyframes beat { 0%, 100% { transform: translate(-50%, -50%) scale(1); opacity: 0.5; } 50% { transform: translate(-50%, -50%) scale(1.1); opacity: 0.8; } }

        .scene { position: relative; width: 380px; max-width: 90vw; height: 320px; transform: scale(1); }
        .geo-animal { position: absolute; opacity: 0; transform: scale(0.3) rotate(-15deg); will-change: transform, opacity; }
        .geo-animal.cat { bottom: 100px; left: 20px; width: 120px; height: 140px; animation: popInAnimal 0.8s cubic-bezier(0.68, -0.55, 0.27, 1.55) 0.5s forwards; }
        .cat .head { width: 75px; height: 70px; background-color: var(--cat-main-color); border-radius: 50%; position: absolute; top: 0; left: 22px; box-shadow: 0 1px 3px rgba(0,0,0,0.2); }
        .cat .ear { width: 0; height: 0; border-left: 20px solid transparent; border-right: 20px solid transparent; border-bottom: 30px solid var(--cat-main-color); position: absolute; top: -10px; }
        .cat .ear.left { left: 5px; transform: rotate(-15deg); } .cat .ear.right { right: 5px; transform: rotate(15deg); }
        .cat .ear-tip-accent { width: 0; height: 0; border-left: 13px solid transparent; border-right: 13px solid transparent; border-bottom: 20px solid var(--cat-accent-color); position: absolute; top: 0px; left: 50%; transform: translateX(-50%); z-index: 1; }
        .cat .eye { width: 12px; height: 18px; background-color: var(--eye-color); border-radius: 50%; position: absolute; top: 23px; }
        .cat .eye.left { left: 18px; } .cat .eye.right { right: 18px; }
        .cat .pupil { width: 6px; height: 6px; background-color: var(--pupil-color); border-radius: 50%; position: absolute; top: 6px; left: 3px; }
        .cat .nose { width: 0; height: 0; border-left: 6px solid transparent; border-right: 6px solid transparent; border-top: 8px solid var(--cat-nose-color); border-radius: 1px; position: absolute; top: 46px; left: 50%; transform: translateX(-50%); }
        .cat .whisker-group { position: absolute; top: 48px; width: 35px; height: 18px; }
        .cat .whisker-group.left { left: -12px; transform: rotate(3deg); } .cat .whisker-group.right { right: -12px; transform: rotate(-3deg) scaleX(-1); }
        .cat .whisker { width: 100%; height: 2px; background-color: var(--cat-accent-color); position: absolute; border-radius: 1px; }
        .cat .whisker:nth-child(1) { top: 0; transform: rotate(-8deg); } .cat .whisker:nth-child(2) { top: 7px; } .cat .whisker:nth-child(3) { top: 14px; transform: rotate(8deg); }
        .cat .body-geo { width: 70px; height: 60px; background-color: var(--cat-main-color); border-radius: 50%; position: absolute; top: 55px; left: 25px; box-shadow: 0 2px 4px rgba(0,0,0,0.2); }
        .cat .paw-geo { width: 22px; height: 40px; background-color: var(--cat-main-color); border-radius: 11px; position: absolute; top: 90px; left: 50px; transform-origin: top center; box-shadow: 0 1px 2px rgba(0,0,0,0.15); }
        .cat .paw-static { width: 22px; height: 38px; background-color: var(--cat-main-color); border-radius: 11px; position: absolute; top: 92px; left: 20px; transform: rotate(10deg); box-shadow: 0 1px 2px rgba(0,0,0,0.15); }

        .geo-animal.dog { bottom: 100px; right: 20px; width: 125px; height: 150px; animation: popInAnimal 0.8s cubic-bezier(0.68, -0.55, 0.27, 1.55) 0.8s forwards; }
        .dog .head { width: 75px; height: 70px; background-color: var(--dog-main-color); border-radius: 50% 50% 45% 45% / 60% 60% 50% 50%; position: absolute; top: 0; left: 25px; box-shadow: 0 1px 3px rgba(0,0,0,0.2); }
        .dog .ear { width: 30px; height: 50px; background-color: var(--dog-main-color); border-radius: 15px 15px 50% 50% / 20px 20px 60% 60%; position: absolute; top: 8px; box-shadow: 0 1px 2px rgba(0,0,0,0.1); }
        .dog .ear-inner { width: 18px; height: 30px; background-color: var(--dog-accent-color); position: absolute; top: 7px; left: 6px; border-radius: 10px 10px 50% 50% / 15px 15px 40% 40%; }
        .dog .ear.left { left: 8px; transform: rotate(-35deg) translateX(-3px); } .dog .ear.right { right: 8px; transform: rotate(35deg) translateX(3px); }
        .dog .eye { width: 13px; height: 13px; background-color: var(--eye-color); border-radius: 50%; position: absolute; top: 28px; }
        .dog .eye.left { left: 20px; } .dog .eye.right { right: 20px; }
        .dog .pupil { width: 7px; height: 7px; background-color: var(--pupil-color); border-radius: 50%; position: absolute; top: 3px; left: 3px; }
        .dog .snout { width: 45px; height: 28px; background-color: var(--dog-accent-color); border-radius: 10px 10px 8px 8px; position: absolute; top: 38px; left: 50%; transform: translateX(-50%); }
        .dog .nose { width: 16px; height: 10px; background-color: var(--dog-nose-color); border-radius: 30% 30% 40% 40%; position: absolute; top: 0px; left: 50%; transform: translateX(-50%); }
        .dog .body-geo { width: 80px; height: 70px; background-color: var(--dog-main-color); border-radius: 45% 45% 50% 50% / 60% 60% 50% 50%; position: absolute; top: 60px; left: 22px; box-shadow: 0 2px 4px rgba(0,0,0,0.2); }
        .dog .chest-patch { width: 38px; height: 25px; background-color: var(--dog-accent-color); border-radius: 10px; position: absolute; top: 10px; left: 50%; transform: translateX(-50%); }
        .dog .paw-geo { width: 25px; height: 45px; background-color: var(--dog-main-color); border-radius: 12px; position: absolute; top: 100px; left: 25px; transform-origin: top center; box-shadow: 0 1px 2px rgba(0,0,0,0.15); }
        .dog .paw-static { width: 25px; height: 42px; background-color: var(--dog-main-color); border-radius: 12px; position: absolute; top: 102px; right: 20px; transform: rotate(-10deg); box-shadow: 0 1px 2px rgba(0,0,0,0.15); }
        @keyframes popInAnimal { to { opacity: 1; transform: scale(1) rotate(0deg); } }
        .geo-animal.cat.touching .paw-geo { animation: catPawTouch 0.7s cubic-bezier(0.34, 1.56, 0.64, 1) forwards; }
        @keyframes catPawTouch { 0% { transform: rotate(0deg) translateY(0px) translateX(0px); } 40% { transform: rotate(-40deg) translateY(8px) translateX(-10px) scale(1.05, 0.95); } 70% { transform: rotate(-10deg) translateY(22px) translateX(-16px) scale(0.95, 1.05); } 100% { transform: rotate(-20deg) translateY(20px) translateX(-15px) scale(1); } }
        .geo-animal.dog.touching .paw-geo { animation: dogPawTouch 0.7s cubic-bezier(0.34, 1.56, 0.64, 1) forwards; }
        @keyframes dogPawTouch { 0% { transform: rotate(0deg) translateY(0px) translateX(0px); } 40% { transform: rotate(40deg) translateY(8px) translateX(10px) scale(1.05, 0.95); } 70% { transform: rotate(10deg) translateY(22px) translateX(16px) scale(0.95, 1.05); } 100% { transform: rotate(20deg) translateY(20px) translateX(15px) scale(1); } }
        
        .envelope-wrapper { position: absolute; bottom: 20px; left: 50%; transform: translateX(-50%) rotateX(10deg); opacity: 0; animation: fadeInEnvelope 0.7s ease-out 1.5s forwards; transform-style: preserve-3d; will-change: transform, opacity; width: 200px; height: 120px; }
        .envelope { position: relative; width: 100%; height: 100%; transform-style: preserve-3d; }
        @keyframes fadeInEnvelope { to { opacity: 1; transform: translateX(-50%) rotateX(0deg); } }
        .envelope-body { width: 100%; height: 100%; background-color: var(--envelope-color); border-radius: 8px; position: absolute; box-shadow: 0 2px 8px rgba(0,0,0,0.2); transform: translateZ(-1px); }
        .envelope-flap { width: 100%; height: 70px; background-color: var(--envelope-flap-color); position: absolute; top: 0; left: 0; clip-path: polygon(0 0, 100% 0, 50% 100%); transform-origin: top center; transition: transform 1s cubic-bezier(0.68, -0.55, 0.27, 1.55); transform: rotateX(0deg); z-index: 2; box-shadow: inset 0 1px 0 rgba(255,255,255,0.2), 0 -1px 2px rgba(0,0,0,0.05) ; }
        .envelope-wrapper.opening .envelope-flap { transform: rotateX(-170deg); }
        .opening-message { position: absolute; top: 55%; left: 50%; width: 80%; transform: translate(-50%, -50%); color: var(--primary-color); font-family: 'Pacifico', cursive; font-size: 1.6em; text-align: center; opacity: 0; transition: opacity 0.5s ease-in 0.7s; text-shadow: 1px 1px 2px rgba(0,0,0,0.1); }
        .envelope-wrapper.opening .opening-message { opacity: 1; }

        .birthday-card { display: none; background: linear-gradient(135deg, var(--secondary-color), var(--accent-color)); padding: 25px 30px; border-radius: 20px; box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15); text-align: center; max-width: 600px; width: 90vw; max-height: 85vh; overflow-y: auto; position: relative; z-index: 10; transform-style: preserve-3d; animation: cardEnterMain 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.2s forwards; opacity: 0; box-sizing: border-box; }
        @keyframes cardEnterMain { 0% { transform: scale(0.8) translateY(50px) rotateY(-30deg); opacity: 0; } 100% { transform: scale(1) translateY(0) rotateY(0deg); opacity: 1; } }
        .card-header h1 { font-family: 'Pacifico', cursive; color: white; font-size: 2.6em; margin-bottom: 15px; text-shadow: 2px 2px 4px rgba(0,0,0,0.2); animation: textPopIn 1s ease-out 0.5s backwards; }
        
        .card-message {
            margin-top: 15px;
            color: var(--text-color);
            font-size: 1em;
            line-height: 1.65;
            min-height: 150px;
            text-align: left;
        }
        .card-message p.message-line {
            margin-bottom: 15px;
            opacity: 0;
            transform: translateY(20px);
            transition: opacity 0.6s ease-out, transform 0.6s ease-out;
            min-height: 1.65em;
            position: relative;
        }
        .card-message p.message-line:last-child {
            margin-bottom: 0;
        }
        .card-message p.message-line.visible {
            opacity: 1;
            transform: translateY(0);
        }
        .cursor {
            display: inline-block;
            background-color: var(--text-color);
            margin-left: 3px;
            width: 2px;
            height: 1.1em;
            animation: blink 0.8s infinite;
            vertical-align: text-bottom;
            opacity: 0;
            transition: opacity 0.1s;
        }
        .cursor.active {
            opacity: 1;
        }
        .cursor.hidden-after-line {
            opacity: 0 !important;
            animation: none !important;
        }
        @keyframes blink {
            0%, 100% { opacity: 1; }
            50% { opacity: 0; }
        }

        .pets-parade { 
            margin-top: 25px; 
            display: flex; 
            justify-content: space-around; 
            align-items: center; 
            opacity: 0;
            flex-wrap: wrap; 
            gap: 10px; 
        }
        .pet { font-size: 2.8em; animation: bounce 1.2s infinite alternate ease-in-out; transition: transform 0.3s ease-in-out; cursor: pointer; }
        .pet.dog-emoji { animation-delay: 0s, 1.8s; } .pet.cat-emoji { animation-delay: 0.25s, 2.05s; }
        .pet:hover { animation-play-state: paused; transform: translateY(-5px) scale(1.15) rotate(3deg); transition: transform 0.3s ease-in-out;  }
        .signature { 
            margin-top: 30px; 
            font-family: 'Pacifico', cursive; 
            font-size: 1.3em; 
            color: white; 
            opacity: 0;
        }

        @keyframes textPopIn { 0% { opacity: 0; transform: scale(0.5) translateY(20px); } 100% { opacity: 1; transform: scale(1) translateY(0); } }
        @keyframes textFadeInUp { 0% { opacity: 0; transform: translateY(30px); } 100% { opacity: 1; transform: translateY(0); } }
        @keyframes petsAppear { 0% { opacity: 0; transform: scale(0.1) rotate(-30deg); } 100% { opacity: 1; transform: scale(1) rotate(0deg); } }
        @keyframes bounce { from { transform: translateY(0px) scale(1); } to { transform: translateY(-8px) scale(1.03); } }

        .paw-print-bg, .confetti { position: fixed; pointer-events: none; }
        .paw-print-bg { font-size: 22px; opacity: 0; animation: walkAndFade 5s linear infinite; }
        @keyframes walkAndFade { 0% { opacity: 0.6; transform: translateY(0) translateX(0) scale(1); } 70% { opacity: 0.2; transform: scale(0.8); } 100% { opacity: 0; transform: translateY(-100px) translateX(20px) scale(0.5); } }
        .confetti { width: 8px; height: 8px; background-color: var(--primary-color); opacity: 0.7; animation: fall 3s linear infinite; border-radius: 50%; }
        .confetti.c2 { background-color: var(--accent-color); animation-duration: 2.5s; animation-delay: 0.5s; }
        .confetti.c3 { background-color: gold; animation-duration: 3.5s; animation-delay: 1s; }
        @keyframes fall { 0% { transform: translateY(-20vh) translateX(0vw) rotate(0deg); opacity: 0.8; } 100% { transform: translateY(120vh) translateX(5vw) rotate(720deg); opacity: 0; } }

        .falling-text-container { position: fixed; top: 0; left: 0; width: 100%; height: 100%; pointer-events: none; z-index: 1; overflow: hidden; }
        .falling-text { position: absolute; font-family: 'Pacifico', cursive; color: var(--primary-color); white-space: nowrap; opacity: 0; text-shadow: 1px 1px 2px rgba(0,0,0,0.1); }
        @keyframes fallAndFadeText { 0% { transform: translateY(-10vh); opacity: 0; } 10% { opacity: 0.9; } 90% { opacity: 0.7; } 100% { transform: translateY(105vh); opacity: 0; } }
        
        .image-popup-modal { display: none; position: fixed; z-index: 1005; left: 0; top: 0; width: 100%; height: 100%; background-color: var(--modal-bg); justify-content: center; align-items: center; padding: 15px; box-sizing: border-box; pointer-events: auto; }
        .image-popup-content { position: relative; display: block; max-width: 300px; max-height: 300px; width: auto; height: auto; object-fit: contain; border-radius: 10px; box-shadow: 0 5px 15px rgba(0,0,0,0.25); animation: popInImage 0.3s ease-out, fadeOutImage 0.5s ease-in forwards; animation-delay: 0s, 3.5s; cursor: pointer; }
        @keyframes popInImage { from { transform: scale(0.5); opacity: 0; } to { transform: scale(1); opacity: 1; } }
        @keyframes fadeOutImage { from { opacity: 1; transform: scale(1); } to { opacity: 0; transform: scale(0.8); } }
        .close-image-popup { position: absolute; top: -10px; right: -10px; background-color: white; color: var(--primary-color); border: 2px solid var(--primary-color); border-radius: 50%; width: 25px; height: 25px; font-size: 16px; line-height: 22px; text-align: center; font-weight: bold; transition: 0.3s; cursor: pointer; box-shadow: 0 2px 5px rgba(0,0,0,0.2); display: none; }
        
        .floating-heart {
            position: fixed;
            font-size: 2em;
            color: var(--primary-color);
            opacity: 0;
            animation: floatUpAndFade 3s linear forwards;
            pointer-events: none;
            z-index: 1010;
            text-shadow: 0 0 5px rgba(255,255,255,0.3);
            will-change: transform, opacity;
        }
        @keyframes floatUpAndFade {
            0% {
                transform: translateY(0) translateX(0) scale(0.5) rotate(0deg);
                opacity: 1;
            }
            100% {
                transform: translateY(-80vh) translateX(calc(var(--random-x, 0) * 30vw - 15vw)) scale(1.2) rotate(calc(var(--random-r, 0) * 90deg - 45deg));
                opacity: 0;
            }
        }

        @media (max-width: 768px) {
            .scene { transform: scale(0.85); } .opening-message { font-size: 1.4em; } .birthday-card { padding: 20px 25px; max-height: 90vh; } .card-header h1 { font-size: 2.2em; } 
            .card-message { font-size: 0.95em; line-height: 1.6; min-height: 130px;} 
            .pet { font-size: 2.5em; } .signature { font-size: 1.2em; }
            .floating-heart { font-size: 1.8em; }
        }
        @media (max-width: 480px) {
            .scene { transform: scale(0.7); height: 300px; } .opening-message { font-size: 1.2em; } .birthday-card { padding: 15px 20px; width: 92vw; max-height: 88vh; } .card-header h1 { font-size: 1.9em; } 
            .card-message { font-size: 0.85em; line-height: 1.55; min-height: 120px;} 
            .card-message p.message-line { margin-bottom: 12px; } 
            .cursor { height: 1em; margin-left: 2px; }
            .pets-parade { margin-top: 20px; gap: 8px; } .pet { font-size: 2.2em; } .signature { font-size: 1.1em; margin-top: 25px; } .confetti { width: 6px; height: 6px; } .paw-print-bg { font-size: 18px; } #initialEnvelope { width: 180px; height: 108px; } #initialEnvelope::after { transform: translate(-50%, -50%) scale(1.6); font-size: 18px;}
            .image-popup-content { max-width: 70vw; max-height: 70vh; }
            .floating-heart { font-size: 1.5em; }
        }
        @media (max-width: 360px) { 
            .scene { transform: scale(0.6); height: 280px; } .opening-message { font-size: 1em; } .card-header h1 { font-size: 1.7em; } 
            .card-message { font-size: 0.8em; min-height: 110px;} 
            .pet { font-size: 2em; } .signature { font-size: 1em; } #initialEnvelope { width: 150px; height: 90px; } #initialEnvelope::after { transform: translate(-50%, -50%) scale(1.4); font-size: 16px;}
        }
    </style>
</head>
<body>
    <div id="initialEnvelope"></div>
    <div id="preloader">
        <div class="beating-heart-bg"></div>
        <div class="scene">
            <div class="geo-animal cat" id="geoCat"><div class="paw-static"></div><div class="body-geo"></div><div class="head"><div class="ear left"><div class="ear-tip-accent"></div></div><div class="ear right"></div><div class="eye left"><div class="pupil"></div></div><div class="eye right"><div class="pupil"></div></div><div class="nose"></div><div class="whisker-group left"><div class="whisker"></div><div class="whisker"></div><div class="whisker"></div></div><div class="whisker-group right"><div class="whisker"></div><div class="whisker"></div><div class="whisker"></div></div></div><div class="paw-geo"></div></div>
            <div class="geo-animal dog" id="geoDog"><div class="paw-static"></div><div class="body-geo"><div class="chest-patch"></div></div><div class="head"><div class="ear left"><div class="ear-inner"></div></div><div class="ear right"><div class="ear-inner"></div></div><div class="eye left"><div class="pupil"></div></div><div class="eye right"><div class="pupil"></div></div><div class="snout"><div class="nose"></div></div></div><div class="paw-geo"></div></div>
            <div class="envelope-wrapper" id="envelopeWrapperPreloader"><div class="envelope"><div class="envelope-body"><span class="opening-message">¡Sorpresa!</span></div><div class="envelope-flap" id="envelopeFlap"></div></div></div>
        </div>
    </div>

    <div id="birthdayCard" class="birthday-card">
        <div class="card-header"><h1>¡Feliz Cumpleaños, <span id="friendNameEl">Wawa</span>!</h1></div>
        <div class="card-message" id="cardMessageContainer">

        </div>
        <div class="pets-parade">
            <span class="pet dog-emoji" data-pet-id="dog1">🐶</span>
            <span class="pet cat-emoji" data-pet-id="cat1">🐱</span>
            <span class="pet dog-emoji" data-pet-id="dog2">🐕</span>
            <span class="pet cat-emoji" data-pet-id="cat2">🐈</span>
        </div>
        <div class="signature">Te quiere mucho,<br><span id="yourNameEl">Fab</span></div>
    </div>

    <div id="petImagePopupModal" class="image-popup-modal">
        <img class="image-popup-content" id="modalPetImagePopup" src="" alt="Pop-up especial">
    </div>
    
    <script>
        const friendName = "Wawita";
        const yourName = "Fab";

        const petImageUrls = {
            dog1: 'https://i.ytimg.com/vi/LXdEwbT2wA8/maxresdefault.jpg',
            cat1: 'https://i.pinimg.com/236x/ba/d1/69/bad1693df2f21c23945a2233a72e66a7.jpg',
            dog2: 'https://st2.depositphotos.com/1146092/7279/i/450/depositphotos_72790263-stock-photo-happy-birthday-dog.jpg',
            cat2: 'https://st2.depositphotos.com/5482604/9088/i/450/depositphotos_90888814-stock-illustration-cat-with-present-in-hat.jpg'
        };
        const imagePopupDuration = 1500;
        let popupTimeoutId = null;

        const typeWriterMessages = [
            {
                id: 'line1',
                text: "¡Feliz Cumpleaños, Wawita! Espero que este día sea especial para ti. Lo disfrutes mucho con tu familia y tus seres queridos.",
                speed: 30,
                delayAfter: 900
            },
            {
                id: 'line2',
                text: "Se que cumpliras todo lo que te propongas wawa y espero en un futuro poder apoyarte en persona :D",
                speed: 30,
                delayAfter: 1000
            },
            {
                id: 'line3',
                text: "Disfrute su dia wawa y por muchisimos mas años de vida, espero le guste este regalo wawita 🥳",
                speed: 35,
                delayAfter: 600
            }
        ];

        function createFallingTextElements() {
             const container = document.createElement('div');
            container.className = 'falling-text-container';
            document.body.insertBefore(container, document.body.firstChild);
            const textContent = "TE QUIERO MUCHO WAWITA";
            const numberOfTexts = 100;
            for (let i = 0; i < numberOfTexts; i++) {
                const span = document.createElement('span');
                span.className = 'falling-text';
                span.textContent = textContent;
                span.style.left = `${Math.random() * 110}vw`;
                const duration = Math.random() * 3 + 5;
                const delay = Math.random() * 10;
                span.style.animationName = 'fallAndFadeText';
                span.style.animationDuration = `${duration}s`;
                span.style.animationDelay = `${delay}s`;
                span.style.animationTimingFunction = 'linear';
                span.style.animationIterationCount = '1'; 
                span.style.animationFillMode = 'forwards'; 
                span.style.fontSize = `${Math.random() * 0.4 + 0.5}em`;
                const randomColor = Math.random();
                if (randomColor < 0.4) span.style.color = 'var(--primary-color)'; 
                else if (randomColor < 0.7) span.style.color = 'var(--accent-color)'; 
                else span.style.color = '#FFD700'; 
                container.appendChild(span);
            }
        }

        function typeSingleLine(messageConfig, onCompleteCallback) {
            const lineElement = document.getElementById(messageConfig.id);
            if (!lineElement) {
                console.error(`Element with id ${messageConfig.id} not found for typewriter.`);
                if (onCompleteCallback) onCompleteCallback();
                return;
            }

            const textContainer = lineElement.querySelector('.text-content');
            const cursorElement = lineElement.querySelector('.cursor');

            if (!textContainer || !cursorElement) {
                console.error(`Required spans (.text-content, .cursor) not found in p#${messageConfig.id}.`);
                if (onCompleteCallback) onCompleteCallback();
                return;
            }
            lineElement.classList.add('visible');
            cursorElement.classList.add('active');

            let charIndex = 0;
            const textToType = messageConfig.text;
            const typingSpeed = messageConfig.speed;

            function typeCharacter() {
                if (charIndex < textToType.length) {
                    textContainer.innerHTML += textToType.charAt(charIndex);
                    charIndex++;
                    setTimeout(typeCharacter, typingSpeed);
                } else {
                    cursorElement.classList.remove('active');
                    cursorElement.classList.add('hidden-after-line');
                    if (onCompleteCallback) {
                        setTimeout(onCompleteCallback, messageConfig.delayAfter);
                    }
                }
            }
            setTimeout(typeCharacter, 600);
        }

        function startTypewriterEffect(messages, finalCallback) {
            const messageContainer = document.getElementById('cardMessageContainer');
            if (!messageContainer) {
                console.error("Card message container not found.");
                if(finalCallback) finalCallback();
                return;
            }
            messageContainer.innerHTML = '';

            messages.forEach(msgConfig => {
                const p = document.createElement('p');
                p.id = msgConfig.id;
                p.classList.add('message-line');
                p.innerHTML = `<span class="text-content"></span><span class="cursor"></span>`;
                messageContainer.appendChild(p);
            });

            let currentMessageIndex = 0;
            function typeNextMessage() {
                if (currentMessageIndex < messages.length) {
                    typeSingleLine(messages[currentMessageIndex], () => {
                        currentMessageIndex++;
                        typeNextMessage();
                    });
                } else {
                    if (finalCallback) finalCallback();
                }
            }
            typeNextMessage();
        }

        function triggerFloatingHearts(count = 15) {
            const existingHearts = document.querySelectorAll('.floating-heart');
            existingHearts.forEach(h => h.remove());

            for (let i = 0; i < count; i++) {
                const heart = document.createElement('div');
                heart.classList.add('floating-heart');
                heart.innerHTML = '❤️'; 
                
                heart.style.left = `${Math.random() * 90 + 5}%`;
                heart.style.top = `${Math.random() * 30 + 70}%`;
                
                heart.style.setProperty('--random-x', Math.random());
                heart.style.setProperty('--random-r', Math.random());

                const duration = Math.random() * 2 + 3;
                heart.style.animationDuration = `${duration}s`;
                heart.style.animationDelay = `${Math.random() * 0.5}s`;

                document.body.appendChild(heart);

                heart.addEventListener('animationend', () => {
                    heart.remove();
                });
            }
        }

        document.addEventListener('DOMContentLoaded', () => {
            document.getElementById('friendNameEl').textContent = friendName;
            document.getElementById('yourNameEl').textContent = yourName;

            const initialEnvelope = document.getElementById('initialEnvelope');
            const preloader = document.getElementById('preloader');
            const birthdayCard = document.getElementById('birthdayCard');
            const geoCat = document.getElementById('geoCat');
            const geoDog = document.getElementById('geoDog');
            const envelopeWrapperPreloader = document.getElementById('envelopeWrapperPreloader');
            
            const TIME_ANIMALS_APPEAR_CSS_CAT = 500;
            const TIME_ANIMALS_APPEAR_CSS_DOG = 800;
            const DELAY_TOUCH_ANIMALS = Math.max(TIME_ANIMALS_APPEAR_CSS_CAT, TIME_ANIMALS_APPEAR_CSS_DOG) + 700; 
            const TIME_PAW_TOUCH_ANIMATION = 700;
            const DELAY_OPEN_ENVELOPE = DELAY_TOUCH_ANIMALS + TIME_PAW_TOUCH_ANIMATION - 200; 
            const TIME_ENVELOPE_FLAP_ANIMATION = 1000;
            const DELAY_FADE_PRELOADER = DELAY_OPEN_ENVELOPE + TIME_ENVELOPE_FLAP_ANIMATION + 300; 

            const petImagePopupModal = document.getElementById('petImagePopupModal');
            const modalPetImagePopup = document.getElementById('modalPetImagePopup');
            const petEmojis = document.querySelectorAll('.pets-parade .pet');
            const petsParadeContainer = document.querySelector('.pets-parade');
            const signatureContainer = document.querySelector('.signature');


            function hideImagePopup() {
                if (petImagePopupModal.style.display === 'flex') {
                    modalPetImagePopup.style.animation = 'none';
                    void modalPetImagePopup.offsetWidth; 
                    modalPetImagePopup.style.animation = `fadeOutImage 0.5s ease-in forwards`;
                    setTimeout(() => {
                        petImagePopupModal.style.display = 'none';
                        modalPetImagePopup.src = ""; 
                        modalPetImagePopup.style.animation = ''; 
                    }, 480); 
                }
                if (popupTimeoutId) {
                    clearTimeout(popupTimeoutId); 
                    popupTimeoutId = null;
                }
            }
            
            petEmojis.forEach(emoji => {
                emoji.addEventListener('click', () => {
                    const petId = emoji.dataset.petId;
                    const imageUrl = petImageUrls[petId];

                    if (imageUrl && (imageUrl.startsWith('http') || imageUrl.startsWith('data:image'))) {
                        if (popupTimeoutId) { 
                            clearTimeout(popupTimeoutId);
                            popupTimeoutId = null;
                             if (petImagePopupModal.style.display === 'flex') {
                                petImagePopupModal.style.display = 'none';
                                modalPetImagePopup.src = "";
                                modalPetImagePopup.style.animation = '';
                             }
                        }
                        
                        modalPetImagePopup.src = imageUrl;
                        modalPetImagePopup.style.animation = 'none'; 
                        void modalPetImagePopup.offsetWidth;
                        modalPetImagePopup.style.animation = `popInImage 0.3s ease-out, fadeOutImage 0.5s ease-in ${imagePopupDuration / 1000 - 0.5}s forwards`;
                        petImagePopupModal.style.display = 'flex';
                        popupTimeoutId = setTimeout(() => {
                            if (petImagePopupModal.style.display === 'flex') { 
                               petImagePopupModal.style.display = 'none';
                               modalPetImagePopup.src = ""; 
                               modalPetImagePopup.style.animation = '';
                            }
                            popupTimeoutId = null;
                        }, imagePopupDuration);
                    } else {
                        console.warn(`No se encontró URL o Data URL válida para el petId: ${petId}`);
                    }
                });
            });

            if (petImagePopupModal) {
                petImagePopupModal.addEventListener('click', () => {
                     hideImagePopup();
                });
            }

            initialEnvelope.style.display = 'block';
            preloader.style.display = 'none';
            preloader.classList.remove('visible', 'hidden');
            birthdayCard.style.display = 'none';

            initialEnvelope.addEventListener('click', () => {
                initialEnvelope.style.display = 'none'; 
                preloader.style.display = 'flex';
                void preloader.offsetWidth; 
                preloader.classList.add('visible');
                preloader.classList.remove('hidden');

                geoCat.style.animation = 'none'; geoDog.style.animation = 'none';
                geoCat.offsetHeight; geoDog.offsetHeight;
                geoCat.style.animation = ''; geoDog.style.animation = '';
                geoCat.classList.remove('touching'); geoDog.classList.remove('touching');
                envelopeWrapperPreloader.classList.remove('opening');
                geoCat.offsetHeight; envelopeWrapperPreloader.offsetHeight;

                setTimeout(() => { geoCat.classList.add('touching'); geoDog.classList.add('touching'); }, DELAY_TOUCH_ANIMALS);
                setTimeout(() => { envelopeWrapperPreloader.classList.add('opening'); }, DELAY_OPEN_ENVELOPE);
                setTimeout(() => { 
                    preloader.classList.add('hidden');
                    preloader.classList.remove('visible');
                    document.body.classList.add('card-visible');
                }, DELAY_FADE_PRELOADER);
            });

            preloader.addEventListener('transitionend', (event) => {
                if (event.propertyName === 'opacity' && preloader.classList.contains('hidden')) {
                    preloader.style.display = 'none'; 
                    birthdayCard.style.display = 'block';
                    birthdayCard.style.opacity = '0';
                    void birthdayCard.offsetWidth; 
                    
                    createFallingTextElements();

                    if(petsParadeContainer) {
                        petsParadeContainer.style.opacity = '0';
                        petsParadeContainer.style.animation = 'none';
                    }
                    if(signatureContainer) {
                        signatureContainer.style.opacity = '0';
                        signatureContainer.style.animation = 'none';
                    }

                    setTimeout(() => {
                        startTypewriterEffect(typeWriterMessages, () => {

                            triggerFloatingHearts(20);

                            if(petsParadeContainer) {
                                petsParadeContainer.style.opacity = '1';
                                petsParadeContainer.style.animation = 'petsAppear 1s ease-out 0.3s forwards';
                            }
                            if(signatureContainer) {
                                signatureContainer.style.opacity = '1';
                                signatureContainer.style.animation = 'textFadeInUp 1s ease-out 0.6s forwards';
                            }
                        });
                    }, 1000);

                    setTimeout(() => { 
                        if (window.innerWidth > 480) { createPawPrints(25); createConfetti(60); } 
                        else { createPawPrints(15); createConfetti(30); }
                    }, 200); 
                }
            });

            function createPawPrints(count = 25) {  
                 document.querySelectorAll('.paw-print-bg').forEach(p => p.remove());
                const pawTypes = ['🐾', '🐾']; 
                for (let i = 0; i < count; i++) {
                    const paw = document.createElement('div');
                    paw.classList.add('paw-print-bg');
                    paw.innerHTML = pawTypes[Math.floor(Math.random() * pawTypes.length)];
                    paw.style.left = Math.random() * 100 + 'vw';
                    paw.style.top = Math.random() * 100 + 'vh';
                    paw.style.animationDelay = Math.random() * 6 + 's';
                    const baseSize = window.innerWidth <= 480 ? 16 : 18;
                    paw.style.fontSize = (Math.random() * (baseSize - 12) + 12) + 'px';
                    paw.style.color = Math.random() > 0.6 ? '#8d6e63' : (Math.random() > 0.3 ? '#bdbdbd' : '#757575');
                    document.body.appendChild(paw);
                }
            }
            function createConfetti(count = 60) { 
                document.querySelectorAll('.confetti').forEach(c => c.remove());
                for (let i = 0; i < count; i++) {
                    const confetti = document.createElement('div');
                    confetti.classList.add('confetti');
                    if (i % 4 === 0) confetti.classList.add('c2');
                    else if (i % 5 === 0) confetti.classList.add('c3');
                    confetti.style.left = Math.random() * 100 + 'vw';
                    confetti.style.top = (Math.random() * 60 - 30) + 'vh';
                    confetti.style.animationDelay = Math.random() * 3.5 + 's';
                    const baseScale = window.innerWidth <= 480 ? 0.4 : 0.5; 
                    confetti.style.transform = `scale(${Math.random() * baseScale + (baseScale + 0.2)})`;
                    document.body.appendChild(confetti);
                }
            }
        });
    </script>
</body>
</html>