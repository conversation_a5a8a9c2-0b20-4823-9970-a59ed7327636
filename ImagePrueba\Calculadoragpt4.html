<!DOCTYPE html>
<html lang="es">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Calculadora Avanzada</title>
  <style>
    :root {
      --bg-light: #fff;
      --bg-dark: #181c24;
      --primary-light: #667eea;
      --primary-dark: #222b3a;
      --accent-light: #764ba2;
      --accent-dark: #0ff;
      --text-light: #222;
      --text-dark: #0ff;
      --button-bg-light: #f3f3f3;
      --button-bg-dark: #232b3b;
      --button-color-light: #333;
      --button-color-dark: #fff;
      --display-bg-light: #222;
      --display-bg-dark: #0ff;
      --display-color-light: #0ff;
      --display-color-dark: #222;
      --operator-bg-light: #7e57c2;
      --operator-bg-dark: #43e97b;
      --equal-bg-light: #43e97b;
      --equal-bg-dark: #ffd600;
      --clear-bg: #ff5252;
      --func-bg: #ffd600;
    }
    body {
      background: linear-gradient(135deg, var(--primary-light), var(--accent-light));
      min-height: 100vh;
      display: flex;
      align-items: center;
      justify-content: center;
      font-family: 'Segoe UI', sans-serif;
      margin: 0;
      transition: background 0.5s;
    }
    body.dark {
      background: linear-gradient(135deg, var(--primary-dark), var(--accent-dark));
    }
    .calculator {
      background: var(--bg-light);
      border-radius: 20px;
      box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
      padding: 30px 20px;
      width: 360px;
      animation: popIn 0.7s cubic-bezier(.68,-0.55,.27,1.55);
      transition: background 0.5s;
    }
    body.dark .calculator {
      background: var(--bg-dark);
    }
    @keyframes popIn {
      0% { transform: scale(0.7); opacity: 0; }
      100% { transform: scale(1); opacity: 1; }
    }
    .display {
      background: var(--display-bg-light);
      color: var(--display-color-light);
      font-size: 2.2em;
      border-radius: 10px;
      padding: 15px;
      text-align: right;
      margin-bottom: 20px;
      min-height: 40px;
      letter-spacing: 1px;
      transition: background 0.3s, color 0.3s;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    body.dark .display {
      background: var(--display-bg-dark);
      color: var(--display-color-dark);
    }
    button {
      padding: 15px 0;
      font-size: 1.1em;
      border: none;
      border-radius: 8px;
      background: var(--button-bg-light);
      color: var(--button-color-light);
      cursor: pointer;
      transition: all 0.2s ease;
      box-shadow: 0 2px 6px rgba(0,0,0,0.07);
      user-select: none;
    }
    body.dark button {
      background: var(--button-bg-dark);
      color: var(--button-color-dark);
    }
    button:active {
      transform: scale(0.97);
    }
    button:hover {
      filter: brightness(1.05);
    }
    .operator { 
      background: var(--operator-bg-light); 
      color: #fff; 
    }
    body.dark .operator { 
      background: var(--operator-bg-dark); 
      color: #222; 
    }
    .equal { 
      background: var(--equal-bg-light); 
      color: #fff; 
      grid-column: span 2;
    }
    body.dark .equal { 
      background: var(--equal-bg-dark); 
      color: #222; 
    }
    .clear { 
      background: var(--clear-bg); 
      color: #fff; 
    }
    .func { 
      background: var(--func-bg); 
      color: #333; 
    }
    .anim {
      animation: pulse 0.4s;
    }
    @keyframes pulse {
      0% { background: #0ff; color: #222; }
      100% { background: #222; color: #0ff; }
    }
    .theme-toggle {
      margin-bottom: 15px;
      display: flex;
      justify-content: flex-end;
      align-items: center;
    }
    .theme-toggle button {
      display: flex;
      align-items: center;
      justify-content: center;
      background: #232b3b;
      color: #ffd600;
      border-radius: 50%;
      width: 38px;
      height: 38px;
      font-size: 1.3em;
      padding: 0;
      transition: background 0.3s, color 0.3s;
    }
    body.dark .theme-toggle button {
      background: #ffd600;
      color: #232b3b;
    }
    .keypad {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: 12px;
    }
    .keypad .row {
      display: contents;
    }
    .functions-row {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: 12px;
      margin-bottom: 12px;
    }
    @media (max-width: 400px) {
      .calculator {
        width: 100%;
        padding: 20px 15px;
        border-radius: 15px;
      }
      button {
        padding: 12px 0;
        font-size: 1em;
      }
    }
  </style>
</head>
<body>
  <div class="calculator">
    <div class="theme-toggle">
      <button id="toggleTheme" title="Modo oscuro/claro">🌙</button>
    </div>
    <div id="display" class="display">0</div>
    
    <!-- Fila de funciones trigonométricas y logarítmicas -->
    <div class="functions-row">
      <button class="func" onclick="insert('Math.sin(')">sin</button>
      <button class="func" onclick="insert('Math.cos(')">cos</button>
      <button class="func" onclick="insert('Math.tan(')">tan</button>
      <button class="func" onclick="insert('Math.log10(')">log</button>
    </div>
    
    <!-- Teclado principal -->
    <div class="keypad">
      <div class="row">
        <button class="clear" onclick="clearDisplay()">C</button>
        <button class="func" onclick="backspace()">⌫</button>
        <button onclick="insert('(')">(</button>
        <button onclick="insert(')')">) </button>
      </div>
      <div class="row">
        <button onclick="insert('7')">7</button>
        <button onclick="insert('8')">8</button>
        <button onclick="insert('9')">9</button>
        <button class="operator" onclick="insert('/')">÷</button>
      </div>
      <div class="row">
        <button onclick="insert('4')">4</button>
        <button onclick="insert('5')">5</button>
        <button onclick="insert('6')">6</button>
        <button class="operator" onclick="insert('*')">×</button>
      </div>
      <div class="row">
        <button onclick="insert('1')">1</button>
        <button onclick="insert('2')">2</button>
        <button onclick="insert('3')">3</button>
        <button class="operator" onclick="insert('-')">−</button>
      </div>
      <div class="row">
        <button onclick="insert('0')">0</button>
        <button onclick="insert('.')">.</button>
        <button class="operator" onclick="insert('+')">+</button>
        <button class="equal" onclick="calculate()">=</button>
      </div>
      <div class="row">
        <button class="func" onclick="insert('**')">xʸ</button>
        <button class="func" onclick="insert('Math.sqrt(')">√</button>
        <button class="func" onclick="insert('Math.PI')">π</button>
        <button class="func" onclick="insert('Math.E')">e</button>
      </div>
    </div>
  </div>
  
  <script>
    let display = document.getElementById('display');
    let current = '';
    const toggleThemeBtn = document.getElementById('toggleTheme');
    
    toggleThemeBtn.onclick = function() {
      document.body.classList.toggle('dark');
      toggleThemeBtn.textContent = document.body.classList.contains('dark') ? '☀️' : '🌙';
    };
    
    function insert(val) {
      if(current === '0' && val !== '.') current = '';
      current += val;
      updateDisplay();
      animateDisplay();
    }
    
    function clearDisplay() {
      current = '';
      updateDisplay();
      animateDisplay();
    }
    
    function backspace() {
      current = current.slice(0, -1);
      updateDisplay();
      animateDisplay();
    }
    
    function updateDisplay() {
      display.textContent = current || '0';
    }
    
    function animateDisplay() {
      display.classList.remove('anim');
      void display.offsetWidth;
      display.classList.add('anim');
    }
    
    function calculate() {
      try {
        let result = eval(current.replace(/÷/g, '/').replace(/×/g, '*'));
        if (typeof result === 'number' && !isNaN(result)) {
          current = result.toString();
        } else {
          current = 'Error';
        }
      } catch {
        current = 'Error';
      }
      updateDisplay();
      animateDisplay();
    }
    
    // Keyboard support
    document.addEventListener('keydown', function(e) {
      if ((e.key >= '0' && e.key <= '9') || '+-*/().'.includes(e.key)) {
        insert(e.key);
      } else if (e.key === 'Enter') {
        calculate();
      } else if (e.key === 'Backspace') {
        backspace();
      } else if (e.key === 'Escape') {
        clearDisplay();
      }
    });
  </script>
</body>
</html>