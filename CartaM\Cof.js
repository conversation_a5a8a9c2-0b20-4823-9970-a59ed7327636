document.addEventListener('DOMContentLoaded', function() {
    const botonAbrir = document.getElementById('boton-abrir');
    const tapa = document.querySelector('.tapa');
    const contenido = document.querySelector('.contenido');
    
    botonAbrir.addEventListener('click', function() {
        tapa.classList.add('abierta');
        
        // Mostrar el contenido después de que la tapa se abra
        setTimeout(function() {
            contenido.classList.add('visible');
        }, 600);
        
        // Cambiar el texto del botón
        botonAbrir.textContent = 'Carta Abierta';
        botonAbrir.disabled = true;
    });
});