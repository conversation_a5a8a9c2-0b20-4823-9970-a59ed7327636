<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ClassroomP - Sistema de Gestión de Aula</title>
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <link rel="stylesheet" href="styles/main.css">
</head>
<body>
    <div class="app-container">
        <!-- Barra lateral de navegación -->
        <nav class="sidebar">
            <div class="sidebar-header">
                <h1>ClassroomP</h1>
            </div>
            <ul class="sidebar-menu">
                <li class="active">
                    <a href="index.html">
                        <span class="material-icons">home</span>
                        <span>Inicio</span>
                    </a>
                </li>
                <li>
                    <a href="cursos.html">
                        <span class="material-icons">school</span>
                        <span>Mis Cursos</span>
                    </a>
                </li>
                <li>
                    <a href="calendario.html">
                        <span class="material-icons">calendar_today</span>
                        <span>Calendario</span>
                    </a>
                </li>
                <li>
                    <a href="tareas.html">
                        <span class="material-icons">assignment</span>
                        <span>Tareas</span>
                    </a>
                </li>
                <li>
                    <a href="mensajes.html">
                        <span class="material-icons">message</span>
                        <span>Mensajes</span>
                    </a>
                </li>
            </ul>
        </nav>

        <!-- Contenido principal -->
        <main class="main-content">
            <header class="content-header">
                <div class="header-title">
                    <h2>Bienvenido a ClassroomP</h2>
                </div>
                <div class="user-menu">
                    <span class="material-icons">notifications</span>
                    <div class="user-profile">
                        <span class="material-icons">account_circle</span>
                        <span>Usuario</span>
                    </div>
                </div>
            </header>

            <div class="dashboard">
                <!-- Sección de cursos recientes -->
                <section class="dashboard-section">
                    <div class="section-header">
                        <h3>Mis Cursos</h3>
                        <a href="cursos.html" class="view-all">Ver todos</a>
                    </div>
                    <div class="courses-grid">
                        <!-- Tarjetas de curso de ejemplo -->
                        <div class="course-card">
                            <div class="course-header">
                                <h4>Matemáticas Avanzadas</h4>
                                <span class="material-icons">more_vert</span>
                            </div>
                            <p class="course-teacher">Prof. García</p>
                            <div class="course-footer">
                                <button class="course-action">
                                    <span class="material-icons">assignment</span>
                                    <span>2 tareas pendientes</span>
                                </button>
                            </div>
                        </div>

                        <div class="course-card">
                            <div class="course-header">
                                <h4>Ciencias Naturales</h4>
                                <span class="material-icons">more_vert</span>
                            </div>
                            <p class="course-teacher">Prof. Martínez</p>
                            <div class="course-footer">
                                <button class="course-action">
                                    <span class="material-icons">assignment</span>
                                    <span>1 tarea pendiente</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Sección de actividad reciente -->
                <section class="dashboard-section">
                    <div class="section-header">
                        <h3>Actividad Reciente</h3>
                    </div>
                    <div class="activity-list">
                        <div class="activity-item">
                            <span class="material-icons">assignment_turned_in</span>
                            <div class="activity-content">
                                <p>Nueva tarea asignada en Matemáticas Avanzadas</p>
                                <span class="activity-time">Hace 2 horas</span>
                            </div>
                        </div>
                        <div class="activity-item">
                            <span class="material-icons">announcement</span>
                            <div class="activity-content">
                                <p>Nuevo anuncio en Ciencias Naturales</p>
                                <span class="activity-time">Hace 3 horas</span>
                            </div>
                        </div>
                    </div>
                </section>
            </div>


        </main>
    </div>
</body>
</html>