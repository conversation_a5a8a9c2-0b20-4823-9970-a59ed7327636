let display = document.getElementById('result');

function appendToDisplay(value) {
    display.value += value;
}

function clearDisplay() {
    display.value = '';
}

function calculateResult() {
    try {
        // Reemplazar x^y con Math.pow(x,y) para evaluación
        let expression = display.value.replace(/(\d+(\.\d+)?)\^(\d+(\.\d+)?)/g, 'Math.pow($1,$3)');
        display.value = eval(expression);
    } catch (error) {
        display.value = 'Error';
    }
}

function calculateFactorial() {
    let num = parseFloat(display.value);
    if (num < 0) {
        display.value = 'Error';
    } else if (num === 0) {
        display.value = 1;
    } else {
        let result = 1;
        for (let i = 1; i <= num; i++) {
            result *= i;
        }
        display.value = result;
    }
}

function calculatePercentage() {
    try {
        display.value = eval(display.value) / 100;
    } catch (error) {
        display.value = 'Error';
    }
}

function toggleDarkMode() {
    document.body.classList.toggle('dark-mode');
}