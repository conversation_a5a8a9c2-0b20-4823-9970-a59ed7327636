<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <title>Gato Divertido CSS</title>
    <style>
        body {
            background: #b2b2b2;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
        }
        .cat-container {
            position: relative;
            width: 320px;
            height: 400px;
        }
        .cat-face {
            position: absolute;
            top: 60px;
            left: 30px;
            width: 260px;
            height: 240px;
            background: #bfae9e;
            border-radius: 50% 50% 60% 60% / 55% 55% 80% 80%;
            z-index: 2;
            box-shadow: 0 8px 0 #a08c7a;
        }
        .cat-ear {
            position: absolute;
            width: 80px;
            height: 100px;
            background: #bfae9e;
            border-radius: 60% 60% 0 0 / 100% 100% 0 0;
            z-index: 1;
        }
        .cat-ear.left {
            left: 0;
            top: 0;
            transform: rotate(-18deg);
        }
        .cat-ear.right {
            right: 0;
            top: 0;
            transform: rotate(18deg);
        }
        .cat-eye {
            position: absolute;
            width: 55px;
            height: 55px;
            background: #fff;
            border-radius: 50%;
            top: 110px;
            z-index: 3;
            border: 2px solid #888;
        }
        .cat-eye.left {
            left: 60px;
            transform: rotate(-10deg);
        }
        .cat-eye.right {
            right: 60px;
            transform: rotate(10deg);
        }
        .cat-pupil {
            position: absolute;
            width: 22px;
            height: 32px;
            background: #e2c600;
            border-radius: 50%;
            top: 15px;
            left: 16px;
            border: 2px solid #444;
            z-index: 4;
        }
        .cat-pupil::after {
            content: "";
            position: absolute;
            width: 8px;
            height: 28px;
            background: #222;
            left: 7px;
            top: 2px;
            border-radius: 50%;
        }
        .cat-nose {
            position: absolute;
            top: 180px;
            left: 125px;
            width: 40px;
            height: 28px;
            background: #e88;
            border-radius: 50% 50% 60% 60% / 60% 60% 100% 100%;
            border: 2px solid #a44;
            z-index: 5;
        }
        .cat-mouth {
            position: absolute;
            top: 200px;
            left: 140px;
            width: 40px;
            height: 30px;
            border-bottom: 4px solid #a44;
            border-radius: 0 0 40px 40px;
            z-index: 5;
        }
        .cat-tongue {
            position: absolute;
            top: 215px;
            left: 150px;
            width: 35px;
            height: 50px;
            background: #e23b6b;
            border-radius: 50% 50% 60% 60% / 60% 60% 100% 100%;
            z-index: 6;
            transform: rotate(10deg);
        }
        .cat-body {
            position: absolute;
            top: 250px;
            left: 60px;
            width: 200px;
            height: 160px;
            background: #bfae9e;
            border-radius: 50% 50% 60% 60% / 80% 80% 100% 100%;
            z-index: 1;
        }
        .cat-paw {
            position: absolute;
            width: 70px;
            height: 80px;
            background: #bfae9e;
            border-radius: 50%;
            z-index: 7;
            border: 2px solid #a08c7a;
        }
        .cat-paw.left {
            left: 10px;
            top: 320px;
            transform: rotate(-18deg);
        }
        .cat-paw.right {
            right: 10px;
            top: 320px;
            transform: rotate(18deg);
        }
        .cat-claw {
            position: absolute;
            width: 18px;
            height: 22px;
            background: #fff;
            border-radius: 60% 60% 100% 100% / 100% 100% 100% 100%;
            border: 2px solid #aaa;
            z-index: 8;
        }
        .cat-paw.left .cat-claw:nth-child(1) { left: 8px; top: 55px; }
        .cat-paw.left .cat-claw:nth-child(2) { left: 26px; top: 60px; }
        .cat-paw.left .cat-claw:nth-child(3) { left: 44px; top: 55px; }
        .cat-paw.right .cat-claw:nth-child(1) { left: 8px; top: 55px; }
        .cat-paw.right .cat-claw:nth-child(2) { left: 26px; top: 60px; }
        .cat-paw.right .cat-claw:nth-child(3) { left: 44px; top: 55px; }
    </style>
</head>
<body>
    <div class="cat-container">
        <div class="cat-ear left"></div>
        <div class="cat-ear right"></div>
        <div class="cat-face"></div>
        <div class="cat-eye left">
            <div class="cat-pupil"></div>
        </div>
        <div class="cat-eye right">
            <div class="cat-pupil"></div>
        </div>
        <div class="cat-nose"></div>
        <div class="cat-mouth"></div>
        <div class="cat-tongue"></div>
        <div class="cat-body"></div>
        <div class="cat-paw left">
            <div class="cat-claw"></div>
            <div class="cat-claw"></div>
            <div class="cat-claw"></div>
        </div>
        <div class="cat-paw right">
            <div class="cat-claw"></div>
            <div class="cat-claw"></div>
            <div class="cat-claw"></div>
        </div>
    </div>
</body>
</html>