<!DOCTYPE html>
<html lang="es">
<head>
   <meta charset="UTF-8">
   <meta name="viewport" content="width=device-width, initial-scale=1.0">
   <title>Calculadora Científica Avanzada</title>
   <style>
       * {
           margin: 0;
           padding: 0;
           box-sizing: border-box;
       }

       body {
           font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
           background: linear-gradient(135deg, #1e3c72, #2a5298, #667eea, #764ba2);
           min-height: 100vh;
           display: flex;
           justify-content: center;
           align-items: center;
           padding: 20px;
           animation: gradientShift 10s ease infinite;
       }

       @keyframes gradientShift {
           0%, 100% { background: linear-gradient(135deg, #1e3c72, #2a5298, #667eea, #764ba2); }
           25% { background: linear-gradient(135deg, #667eea, #764ba2, #f093fb, #f5576c); }
           50% { background: linear-gradient(135deg, #f093fb, #f5576c, #4facfe, #00f2fe); }
           75% { background: linear-gradient(135deg, #4facfe, #00f2fe, #43e97b, #38f9d7); }
       }

       .calculator {
           background: rgba(255, 255, 255, 0.1);
           backdrop-filter: blur(20px);
           border-radius: 25px;
           padding: 30px;
           box-shadow: 0 25px 45px rgba(0, 0, 0, 0.2);
           border: 1px solid rgba(255, 255, 255, 0.2);
           animation: slideIn 1s ease-out;
           max-width: 400px;
           width: 100%;
       }

       @keyframes slideIn {
           from {
               opacity: 0;
               transform: translateY(-100px) scale(0.8);
           }
           to {
               opacity: 1;
               transform: translateY(0) scale(1);
           }
       }

       .display-container {
           margin-bottom: 25px;
           background: rgba(0, 0, 0, 0.3);
           border-radius: 15px;
           padding: 20px;
           position: relative;
           overflow: hidden;
       }

       .display-container::before {
           content: '';
           position: absolute;
           top: 0;
           left: -100%;
           width: 100%;
           height: 100%;
           background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
           animation: shimmer 3s infinite;
       }

       @keyframes shimmer {
           0% { left: -100%; }
           100% { left: 100%; }
       }

       .mode-display {
           color: #00ff88;
           font-size: 12px;
           margin-bottom: 5px;
           text-align: right;
           opacity: 0.8;
       }

       .history-display {
           color: #ffffff;
           opacity: 0.6;
           font-size: 14px;
           min-height: 20px;
           text-align: right;
           margin-bottom: 5px;
           overflow: hidden;
           text-overflow: ellipsis;
       }

       .main-display {
           color: #ffffff;
           font-size: 28px;
           font-weight: 300;
           text-align: right;
           min-height: 40px;
           overflow: hidden;
           text-overflow: ellipsis;
           transition: all 0.3s ease;
       }

       .error {
           color: #ff4757 !important;
           animation: shake 0.5s ease-in-out;
       }

       @keyframes shake {
           0%, 100% { transform: translateX(0); }
           25% { transform: translateX(-5px); }
           75% { transform: translateX(5px); }
       }

       .mode-selector {
           display: flex;
           margin-bottom: 20px;
           background: rgba(0, 0, 0, 0.2);
           border-radius: 12px;
           padding: 5px;
       }

       .mode-btn {
           flex: 1;
           padding: 10px;
           background: transparent;
           color: rgba(255, 255, 255, 0.7);
           border: none;
           border-radius: 8px;
           cursor: pointer;
           transition: all 0.3s ease;
           font-size: 12px;
       }

       .mode-btn.active {
           background: linear-gradient(135deg, #667eea, #764ba2);
           color: white;
           transform: scale(1.05);
       }

       .buttons-grid {
           display: grid;
           grid-template-columns: repeat(5, 1fr);
           gap: 10px;
           transition: all 0.5s ease;
       }

       .btn {
           padding: 15px;
           border: none;
           border-radius: 12px;
           cursor: pointer;
           font-size: 16px;
           font-weight: 500;
           transition: all 0.2s ease;
           position: relative;
           overflow: hidden;
           backdrop-filter: blur(10px);
       }

       .btn::before {
           content: '';
           position: absolute;
           top: 50%;
           left: 50%;
           width: 0;
           height: 0;
           background: rgba(255, 255, 255, 0.3);
           border-radius: 50%;
           transform: translate(-50%, -50%);
           transition: all 0.3s ease;
       }

       .btn:active::before {
           width: 100px;
           height: 100px;
       }

       .btn:hover {
           transform: translateY(-2px);
           box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
       }

       .btn:active {
           transform: translateY(0);
       }

       .number {
           background: rgba(255, 255, 255, 0.15);
           color: white;
           border: 1px solid rgba(255, 255, 255, 0.2);
       }

       .operator {
           background: linear-gradient(135deg, #ff6b6b, #ee5a24);
           color: white;
           font-weight: bold;
       }

       .function {
           background: linear-gradient(135deg, #4834d4, #686de0);
           color: white;
           font-size: 12px;
       }

       .equals {
           background: linear-gradient(135deg, #00d2d3, #54a0ff);
           color: white;
           font-weight: bold;
           grid-column: span 2;
       }

       .clear {
           background: linear-gradient(135deg, #ff9ff3, #f368e0);
           color: white;
           font-weight: bold;
       }

       .zero {
           grid-column: span 2;
       }

       .memory-panel {
           margin-top: 20px;
           background: rgba(0, 0, 0, 0.2);
           border-radius: 12px;
           padding: 15px;
           display: none;
       }

       .memory-panel.active {
           display: block;
           animation: fadeIn 0.3s ease;
       }

       @keyframes fadeIn {
           from { opacity: 0; transform: translateY(10px); }
           to { opacity: 1; transform: translateY(0); }
       }

       .memory-display {
           color: white;
           font-size: 14px;
           margin-bottom: 10px;
           max-height: 100px;
           overflow-y: auto;
       }

       .memory-item {
           padding: 5px;
           margin: 2px 0;
           background: rgba(255, 255, 255, 0.1);
           border-radius: 5px;
           cursor: pointer;
           transition: all 0.2s ease;
       }

       .memory-item:hover {
           background: rgba(255, 255, 255, 0.2);
           transform: translateX(5px);
       }

       .history-panel {
           position: fixed;
           right: -300px;
           top: 0;
           width: 300px;
           height: 100vh;
           background: rgba(0, 0, 0, 0.9);
           backdrop-filter: blur(20px);
           padding: 20px;
           transition: all 0.3s ease;
           z-index: 1000;
       }

       .history-panel.active {
           right: 0;
       }

       .history-header {
           color: white;
           font-size: 18px;
           margin-bottom: 20px;
           display: flex;
           justify-content: space-between;
           align-items: center;
       }

       .close-history {
           background: none;
           border: none;
           color: white;
           font-size: 24px;
           cursor: pointer;
       }

       .history-list {
           color: white;
           max-height: calc(100vh - 100px);
           overflow-y: auto;
       }

       .history-item {
           padding: 10px;
           margin-bottom: 10px;
           background: rgba(255, 255, 255, 0.1);
           border-radius: 8px;
           cursor: pointer;
           transition: all 0.2s ease;
       }

       .history-item:hover {
           background: rgba(255, 255, 255, 0.2);
           transform: scale(1.02);
       }

       .angle-indicator {
           position: absolute;
           top: 10px;
           left: 10px;
           color: #00ff88;
           font-size: 10px;
           opacity: 0.8;
       }

       .scientific-mode {
           grid-template-columns: repeat(6, 1fr);
       }

       .programmer-mode {
           grid-template-columns: repeat(4, 1fr);
       }

       .base-display {
           display: none;
           margin-top: 10px;
           font-size: 12px;
           color: rgba(255, 255, 255, 0.7);
       }

       .base-display.active {
           display: block;
       }

       .graph-container {
           margin-top: 20px;
           background: rgba(0, 0, 0, 0.3);
           border-radius: 12px;
           padding: 15px;
           display: none;
       }

       .graph-container.active {
           display: block;
           animation: fadeIn 0.3s ease;
       }

       .graph-canvas {
           width: 100%;
           height: 200px;
           background: rgba(255, 255, 255, 0.1);
           border-radius: 8px;
       }

       @media (max-width: 480px) {
           .calculator {
               padding: 20px;
               margin: 10px;
           }
           
           .btn {
               padding: 12px;
               font-size: 14px;
           }
           
           .main-display {
               font-size: 24px;
           }
           
           .buttons-grid {
               gap: 8px;
           }
       }
   </style>
</head>
<body>
   <div class="calculator">
       <div class="angle-indicator" id="angleIndicator">DEG</div>
       
       <div class="display-container">
           <div class="mode-display" id="modeDisplay">Estándar</div>
           <div class="history-display" id="historyDisplay"></div>
           <div class="main-display" id="mainDisplay">0</div>
           <div class="base-display" id="baseDisplay">
               <div>BIN: <span id="binDisplay">0</span></div>
               <div>OCT: <span id="octDisplay">0</span></div>
               <div>HEX: <span id="hexDisplay">0</span></div>
           </div>
       </div>

       <div class="mode-selector">
           <button class="mode-btn active" onclick="setMode('standard')">Estándar</button>
           <button class="mode-btn" onclick="setMode('scientific')">Científica</button>
           <button class="mode-btn" onclick="setMode('programmer')">Programador</button>
       </div>

       <div class="buttons-grid" id="buttonsGrid">
           <!-- Los botones se generarán dinámicamente -->
       </div>

       <div class="memory-panel" id="memoryPanel">
           <div class="memory-display" id="memoryDisplay">
               <div>Memoria: 0</div>
           </div>
           <div style="display: flex; gap: 10px;">
               <button class="btn function" onclick="memoryRecall()">MR</button>
               <button class="btn function" onclick="memoryClear()">MC</button>
               <button class="btn function" onclick="memoryAdd()">M+</button>
               <button class="btn function" onclick="memorySubtract()">M-</button>
               <button class="btn function" onclick="memoryStore()">MS</button>
           </div>
       </div>

       <div class="graph-container" id="graphContainer">
           <canvas class="graph-canvas" id="graphCanvas"></canvas>
           <div style="margin-top: 10px;">
               <input type="text" id="functionInput" placeholder="f(x) = x^2" 
                      style="width: 70%; padding: 8px; border-radius: 5px; border: none; background: rgba(255,255,255,0.1); color: white;">
               <button class="btn function" onclick="plotFunction()" style="width: 25%; margin-left: 5%;">Graficar</button>
           </div>
       </div>
   </div>

   <div class="history-panel" id="historyPanel">
       <div class="history-header">
           <span>Historial</span>
           <button class="close-history" onclick="toggleHistory()">&times;</button>
       </div>
       <div class="history-list" id="historyList"></div>
   </div>

   <script>
       class AdvancedCalculator {
           constructor() {
               this.display = document.getElementById('mainDisplay');
               this.historyDisplay = document.getElementById('historyDisplay');
               this.modeDisplay = document.getElementById('modeDisplay');
               this.angleIndicator = document.getElementById('angleIndicator');
               this.buttonsGrid = document.getElementById('buttonsGrid');
               
               this.currentInput = '0';
               this.previousInput = '';
               this.operator = '';
               this.waitingForNewInput = false;
               this.mode = 'standard';
               this.angleMode = 'deg'; // deg, rad, grad
               this.memory = 0;
               this.history = [];
               this.constants = {
                   pi: Math.PI,
                   e: Math.E,
                   phi: (1 + Math.sqrt(5)) / 2
               };
               
               this.init();
           }

           init() {
               this.setMode('standard');
               this.setupKeyboardListeners();
               this.updateDisplay();
           }

           setMode(mode) {
               this.mode = mode;
               this.modeDisplay.textContent = mode === 'standard' ? 'Estándar' : 
                                            mode === 'scientific' ? 'Científica' : 'Programador';
               
               // Actualizar clase del grid
               this.buttonsGrid.className = 'buttons-grid';
               if (mode === 'scientific') {
                   this.buttonsGrid.classList.add('scientific-mode');
               } else if (mode === 'programmer') {
                   this.buttonsGrid.classList.add('programmer-mode');
               }
               
               // Mostrar/ocultar paneles específicos
               document.getElementById('memoryPanel').classList.toggle('active', mode !== 'standard');
               document.getElementById('baseDisplay').classList.toggle('active', mode === 'programmer');
               document.getElementById('graphContainer').classList.toggle('active', mode === 'scientific');
               
               this.generateButtons();
               this.updateModeButtons();
           }

           updateModeButtons() {
               document.querySelectorAll('.mode-btn').forEach(btn => {
                   btn.classList.remove('active');
               });
               document.querySelector(`.mode-btn[onclick="setMode('${this.mode}')"]`).classList.add('active');
           }

           generateButtons() {
               const standardButtons = [
                   {text: 'C', class: 'clear', action: 'clear'},
                   {text: '±', class: 'operator', action: 'toggleSign'},
                   {text: '%', class: 'operator', action: 'percent'},
                   {text: '÷', class: 'operator', action: 'operator', value: '/'},
                   {text: '⌫', class: 'operator', action: 'backspace'},
                   
                   {text: '7', class: 'number', action: 'number', value: '7'},
                   {text: '8', class: 'number', action: 'number', value: '8'},
                   {text: '9', class: 'number', action: 'number', value: '9'},
                   {text: '×', class: 'operator', action: 'operator', value: '*'},
                   {text: '📊', class: 'function', action: 'toggleHistory'},
                   
                   {text: '4', class: 'number', action: 'number', value: '4'},
                   {text: '5', class: 'number', action: 'number', value: '5'},
                   {text: '6', class: 'number', action: 'number', value: '6'},
                   {text: '-', class: 'operator', action: 'operator', value: '-'},
                   {text: '√', class: 'function', action: 'function', value: 'sqrt'},
                   
                   {text: '1', class: 'number', action: 'number', value: '1'},
                   {text: '2', class: 'number', action: 'number', value: '2'},
                   {text: '3', class: 'number', action: 'number', value: '3'},
                   {text: '+', class: 'operator', action: 'operator', value: '+'},
                   {text: 'x²', class: 'function', action: 'function', value: 'square'},
                   
                   {text: '0', class: 'number zero', action: 'number', value: '0'},
                   {text: '.', class: 'number', action: 'decimal'},
                   {text: '=', class: 'equals', action: 'equals'}
               ];

               const scientificButtons = [
                   {text: 'C', class: 'clear', action: 'clear'},
                   {text: '±', class: 'operator', action: 'toggleSign'},
                   {text: 'DEG', class: 'function', action: 'toggleAngleMode'},
                   {text: 'sin', class: 'function', action: 'function', value: 'sin'},
                   {text: 'cos', class: 'function', action: 'function', value: 'cos'},
                   {text: 'tan', class: 'function', action: 'function', value: 'tan'},
                   
                   {text: '7', class: 'number', action: 'number', value: '7'},
                   {text: '8', class: 'number', action: 'number', value: '8'},
                   {text: '9', class: 'number', action: 'number', value: '9'},
                   {text: '÷', class: 'operator', action: 'operator', value: '/'},
                   {text: 'ln', class: 'function', action: 'function', value: 'ln'},
                   {text: 'log', class: 'function', action: 'function', value: 'log'},
                   
                   {text: '4', class: 'number', action: 'number', value: '4'},
                   {text: '5', class: 'number', action: 'number', value: '5'},
                   {text: '6', class: 'number', action: 'number', value: '6'},
                   {text: '×', class: 'operator', action: 'operator', value: '*'},
                   {text: 'x²', class: 'function', action: 'function', value: 'square'},
                   {text: 'x³', class: 'function', action: 'function', value: 'cube'},
                   
                   {text: '1', class: 'number', action: 'number', value: '1'},
                   {text: '2', class: 'number', action: 'number', value: '2'},
                   {text: '3', class: 'number', action: 'number', value: '3'},
                   {text: '-', class: 'operator', action: 'operator', value: '-'},
                   {text: '√', class: 'function', action: 'function', value: 'sqrt'},
                   {text: 'π', class: 'function', action: 'constant', value: 'pi'},
                   
                   {text: '0', class: 'number', action: 'number', value: '0'},
                   {text: '.', class: 'number', action: 'decimal'},
                   {text: '+', class: 'operator', action: 'operator', value: '+'},
                   {text: 'e', class: 'function', action: 'constant', value: 'e'},
                   {text: '=', class: 'equals', action: 'equals'},
                   {text: '📊', class: 'function', action: 'toggleHistory'}
               ];

               const programmerButtons = [
                   {text: 'C', class: 'clear', action: 'clear'},
                   {text: 'A', class: 'number', action: 'number', value: 'A'},
                   {text: 'B', class: 'number', action: 'number', value: 'B'},
                   {text: 'C', class: 'number', action: 'number', value: 'C'},
                   
                   {text: 'D', class: 'number', action: 'number', value: 'D'},
                   {text: 'E', class: 'number', action: 'number', value: 'E'},
                   {text: 'F', class: 'number', action: 'number', value: 'F'},
                   {text: 'AND', class: 'operator', action: 'bitwiseOp', value: '&'},
                   
                   {text: '7', class: 'number', action: 'number', value: '7'},
                   {text: '8', class: 'number', action: 'number', value: '8'},
                   {text: '9', class: 'number', action: 'number', value: '9'},
                   {text: 'OR', class: 'operator', action: 'bitwiseOp', value: '|'},
                   
                   {text: '4', class: 'number', action: 'number', value: '4'},
                   {text: '5', class: 'number', action: 'number', value: '5'},
                   {text: '6', class: 'number', action: 'number', value: '6'},
                   {text: 'XOR', class: 'operator', action: 'bitwiseOp', value: '^'},
                   
                   {text: '1', class: 'number', action: 'number', value: '1'},
                   {text: '2', class: 'number', action: 'number', value: '2'},
                   {text: '3', class: 'number', action: 'number', value: '3'},
                   {text: 'NOT', class: 'operator', action: 'bitwiseOp', value: '~'},
                   
                   {text: '0', class: 'number zero', action: 'number', value: '0'},
                   {text: '=', class: 'equals', action: 'equals'}
               ];

               let buttons;
               switch(this.mode) {
                   case 'scientific': buttons = scientificButtons; break;
                   case 'programmer': buttons = programmerButtons; break;
                   default: buttons = standardButtons;
               }

               this.buttonsGrid.innerHTML = '';
               buttons.forEach(btn => {
                   const button = document.createElement('button');
                   button.className = `btn ${btn.class}`;
                   button.textContent = btn.text;
                   button.onclick = () => this.handleButtonClick(btn.action, btn.value || btn.text);
                   this.buttonsGrid.appendChild(button);
               });
           }

           handleButtonClick(action, value) {
               // Animación de botón
               event.target.style.transform = 'scale(0.95)';
               setTimeout(() => {
                   event.target.style.transform = '';
               }, 100);

               switch(action) {
                   case 'number':
                       this.inputNumber(value);
                       break;
                   case 'operator':
                       this.inputOperator(value);
                       break;
                   case 'decimal':
                       this.inputDecimal();
                       break;
                   case 'equals':
                       this.calculate();
                       break;
                   case 'clear':
                       this.clear();
                       break;
                   case 'backspace':
                       this.backspace();
                       break;
                   case 'toggleSign':
                       this.toggleSign();
                       break;
                   case 'percent':
                       this.percent();
                       break;
                   case 'function':
                       this.applyFunction(value);
                       break;
                   case 'constant':
                       this.inputConstant(value);
                       break;
                   case 'toggleAngleMode':
                       this.toggleAngleMode();
                       break;
                   case 'toggleHistory':
                       this.toggleHistory();
                       break;
                   case 'bitwiseOp':
                       this.inputBitwiseOperator(value);
                       break;
               }

               this.updateDisplay();
           }

           inputNumber(num) {
               if (this.waitingForNewInput) {
                   this.currentInput = num;
                   this.waitingForNewInput = false;
               } else {
                   this.currentInput = this.currentInput === '0' ? num : this.currentInput + num;
               }
               
               if (this.mode === 'programmer') {
                   this.updateBaseDisplays();
               }
           }

           inputOperator(op) {
               if (!this.waitingForNewInput && this.previousInput !== '' && this.operator !== '') {
                   this.calculate();
               }
               
               this.previousInput = this.currentInput;
               this.operator = op;
               this.waitingForNewInput = true;
               this.historyDisplay.textContent = `${this.previousInput} ${this.getOperatorSymbol(op)}`;
           }

           inputBitwiseOperator(op) {
               this.inputOperator(op);
           }

           getOperatorSymbol(op) {
               const symbols = {
                   '+': '+',
                   '-': '-',
                   '*': '×',
                   '/': '÷',
                   '&': 'AND',
                   '|': 'OR',
                   '^': 'XOR',
                   '~': 'NOT'
               };
               return symbols[op] || op;
           }

           inputDecimal() {
               if (this.waitingForNewInput) {
                   this.currentInput = '0.';
                   this.waitingForNewInput = false;
               } else if (this.currentInput.indexOf('.') === -1) {
                   this.currentInput += '.';
               }
           }

           calculate() {
               if (this.previousInput === '' || this.operator === '') return;

               let result;
               const prev = parseFloat(this.previousInput);
               const current = parseFloat(this.currentInput);

               try {
                   switch(this.operator) {
                       case '+':
                           result = prev + current;
                           break;
                       case '-':
                           result = prev - current;
                           break;
                       case '*':
                           result = prev * current;
                           break;
                       case '/':
                           if (current === 0) throw new Error('División por cero');
                           result = prev / current;
                           break;
                       case '&':
                           result = Math.floor(prev) & Math.floor(current);
                           break;
                       case '|':
                           result = Math.floor(prev) | Math.floor(current);
                           break;
                       case '^':
                           result = Math.floor(prev) ^ Math.floor(current);
                           break;
                       default:
                           return;
                   }

                   const calculation = `${this.previousInput} ${this.getOperatorSymbol(this.operator)} ${this.currentInput} = ${result}`;
                   this.addToHistory(calculation);

                   this.currentInput = result.toString();
                   this.previousInput = '';
                   this.operator = '';
                   this.waitingForNewInput = true;
                   this.historyDisplay.textContent = calculation;

               } catch (error) {
                   this.displayError(error.message);
               }
           }

           applyFunction(func) {
               const current = parseFloat(this.currentInput);
               let result;

               try {
                   switch(func) {
                       case 'sin':
                           result = Math.sin(this.angleMode === 'deg' ? current * Math.PI / 180 : current);
                           break;
                       case 'cos':
                           result = Math.cos(this.angleMode === 'deg' ? current * Math.PI / 180 : current);
                           break;
                       case 'tan':
                           result = Math.tan(this.angleMode === 'deg' ? current * Math.PI / 180 : current);
                           break;
                       case 'ln':
                           if (current <= 0) throw new Error('Logaritmo de número no positivo');
                           result = Math.log(current);
                           break;
                       case 'log':
                           if (current <= 0) throw new Error('Logaritmo de número no positivo');
                           result = Math.log10(current);
                           break;
                       case 'sqrt':
                           if (current < 0) throw new Error('Raíz cuadrada de número negativo');
                           result = Math.sqrt(current);
                           break;
                       case 'square':
                           result = current * current;
                           break;
                       case 'cube':
                           result = current * current * current;
                           break;
                       default:
                           return;
                        }

const calculation = `${func}(${this.currentInput}) = ${result}`;
this.addToHistory(calculation);

this.currentInput = result.toString();
this.waitingForNewInput = true;
this.historyDisplay.textContent = calculation;

} catch (error) {
this.displayError(error.message);
}
}

inputConstant(constant) {
this.currentInput = this.constants[constant].toString();
this.waitingForNewInput = true;
}

clear() {
this.currentInput = '0';
this.previousInput = '';
this.operator = '';
this.waitingForNewInput = false;
this.historyDisplay.textContent = '';
this.clearError();
}

backspace() {
if (this.currentInput.length > 1) {
this.currentInput = this.currentInput.slice(0, -1);
} else {
this.currentInput = '0';
}
}

toggleSign() {
if (this.currentInput !== '0') {
this.currentInput = this.currentInput.startsWith('-') ? 
    this.currentInput.substring(1) : '-' + this.currentInput;
}
}

percent() {
this.currentInput = (parseFloat(this.currentInput) / 100).toString();
this.waitingForNewInput = true;
}

toggleAngleMode() {
const modes = ['deg', 'rad', 'grad'];
const currentIndex = modes.indexOf(this.angleMode);
this.angleMode = modes[(currentIndex + 1) % modes.length];
this.angleIndicator.textContent = this.angleMode.toUpperCase();

// Animar el indicador
this.angleIndicator.style.transform = 'scale(1.2)';
setTimeout(() => {
this.angleIndicator.style.transform = 'scale(1)';
}, 200);
}

updateDisplay() {
const formatted = this.formatNumber(this.currentInput);
this.display.textContent = formatted;

if (this.mode === 'programmer') {
this.updateBaseDisplays();
}
}

formatNumber(num) {
if (num === '' || isNaN(num)) return '0';

const number = parseFloat(num);
if (Math.abs(number) > 1e15 || (Math.abs(number) < 1e-6 && number !== 0)) {
return number.toExponential(6);
}

// Limitar decimales para evitar desbordamiento visual
if (num.includes('.')) {
const parts = num.split('.');
if (parts[1].length > 8) {
    return parseFloat(num).toFixed(8);
}
}

return num;
}

updateBaseDisplays() {
const num = parseInt(this.currentInput) || 0;
document.getElementById('binDisplay').textContent = num.toString(2);
document.getElementById('octDisplay').textContent = num.toString(8);
document.getElementById('hexDisplay').textContent = num.toString(16).toUpperCase();
}

displayError(message) {
this.display.textContent = 'Error';
this.display.classList.add('error');
this.historyDisplay.textContent = message;
this.currentInput = '0';
this.waitingForNewInput = true;
}

clearError() {
this.display.classList.remove('error');
}

addToHistory(calculation) {
this.history.unshift(calculation);
if (this.history.length > 50) {
this.history.pop();
}
this.updateHistoryPanel();
}

updateHistoryPanel() {
const historyList = document.getElementById('historyList');
historyList.innerHTML = '';

this.history.forEach((item, index) => {
const historyItem = document.createElement('div');
historyItem.className = 'history-item';
historyItem.textContent = item;
historyItem.onclick = () => {
    const result = item.split('=').pop().trim();
    this.currentInput = result;
    this.waitingForNewInput = true;
    this.updateDisplay();
    this.toggleHistory();
};
historyList.appendChild(historyItem);
});
}

toggleHistory() {
const historyPanel = document.getElementById('historyPanel');
historyPanel.classList.toggle('active');
}

// Funciones de memoria
memoryRecall() {
this.currentInput = this.memory.toString();
this.waitingForNewInput = true;
this.updateDisplay();
}

memoryClear() {
this.memory = 0;
this.updateMemoryDisplay();
}

memoryAdd() {
this.memory += parseFloat(this.currentInput) || 0;
this.updateMemoryDisplay();
}

memorySubtract() {
this.memory -= parseFloat(this.currentInput) || 0;
this.updateMemoryDisplay();
}

memoryStore() {
this.memory = parseFloat(this.currentInput) || 0;
this.updateMemoryDisplay();
}

updateMemoryDisplay() {
const memoryDisplay = document.getElementById('memoryDisplay');
memoryDisplay.innerHTML = `<div>Memoria: ${this.memory}</div>`;
}

setupKeyboardListeners() {
document.addEventListener('keydown', (e) => {
e.preventDefault();

const key = e.key;

if (key >= '0' && key <= '9') {
    this.handleButtonClick('number', key);
} else if (key === '.') {
    this.handleButtonClick('decimal');
} else if (['+', '-', '*', '/'].includes(key)) {
    this.handleButtonClick('operator', key);
} else if (key === 'Enter' || key === '=') {
    this.handleButtonClick('equals');
} else if (key === 'Escape' || key.toLowerCase() === 'c') {
    this.handleButtonClick('clear');
} else if (key === 'Backspace') {
    this.handleButtonClick('backspace');
} else if (key === '%') {
    this.handleButtonClick('percent');
}
});
}
}

// Función para graficar
function plotFunction() {
const canvas = document.getElementById('graphCanvas');
const ctx = canvas.getContext('2d');
const functionInput = document.getElementById('functionInput').value;

canvas.width = canvas.offsetWidth;
canvas.height = canvas.offsetHeight;

ctx.clearRect(0, 0, canvas.width, canvas.height);

// Dibujar ejes
ctx.strokeStyle = 'rgba(255, 255, 255, 0.3)';
ctx.lineWidth = 1;

// Eje X
ctx.beginPath();
ctx.moveTo(0, canvas.height / 2);
ctx.lineTo(canvas.width, canvas.height / 2);
ctx.stroke();

// Eje Y
ctx.beginPath();
ctx.moveTo(canvas.width / 2, 0);
ctx.lineTo(canvas.width / 2, canvas.height);
ctx.stroke();

// Dibujar cuadrícula
ctx.strokeStyle = 'rgba(255, 255, 255, 0.1)';
for (let i = 0; i < canvas.width; i += 20) {
ctx.beginPath();
ctx.moveTo(i, 0);
ctx.lineTo(i, canvas.height);
ctx.stroke();
}
for (let i = 0; i < canvas.height; i += 20) {
ctx.beginPath();
ctx.moveTo(0, i);
ctx.lineTo(canvas.width, i);
ctx.stroke();
}

// Graficar función
try {
ctx.strokeStyle = '#00ff88';
ctx.lineWidth = 2;
ctx.beginPath();

let firstPoint = true;
const scale = 20; // píxeles por unidad

for (let px = 0; px < canvas.width; px++) {
const x = (px - canvas.width / 2) / scale;
let y;

// Evaluar función simple
if (functionInput.includes('x^2') || functionInput.includes('x²')) {
    y = x * x;
} else if (functionInput.includes('x^3') || functionInput.includes('x³')) {
    y = x * x * x;
} else if (functionInput.includes('sin')) {
    y = Math.sin(x);
} else if (functionInput.includes('cos')) {
    y = Math.cos(x);
} else if (functionInput.includes('tan')) {
    y = Math.tan(x);
} else if (functionInput.includes('ln')) {
    y = x > 0 ? Math.log(x) : NaN;
} else if (functionInput.includes('sqrt')) {
    y = x >= 0 ? Math.sqrt(x) : NaN;
} else {
    // Función lineal por defecto
    y = x;
}

if (!isNaN(y) && isFinite(y)) {
    const py = canvas.height / 2 - y * scale;
    
    if (py >= 0 && py <= canvas.height) {
        if (firstPoint) {
            ctx.moveTo(px, py);
            firstPoint = false;
        } else {
            ctx.lineTo(px, py);
        }
    }
}
}

ctx.stroke();
} catch (error) {
console.error('Error al graficar función:', error);
}
}

// Funciones globales para la interfaz
function setMode(mode) {
calculator.setMode(mode);
}

function toggleHistory() {
calculator.toggleHistory();
}

function memoryRecall() {
calculator.memoryRecall();
}

function memoryClear() {
calculator.memoryClear();
}

function memoryAdd() {
calculator.memoryAdd();
}

function memorySubtract() {
calculator.memorySubtract();
}

function memoryStore() {
calculator.memoryStore();
}

// Inicializar calculadora
const calculator = new AdvancedCalculator();

// Animaciones adicionales
setInterval(() => {
if (Math.random() > 0.95) {
document.querySelector('.calculator').style.boxShadow = 
'0 25px 45px rgba(0, 0, 0, 0.2), 0 0 20px rgba(255, 255, 255, 0.1)';
setTimeout(() => {
document.querySelector('.calculator').style.boxShadow = 
    '0 25px 45px rgba(0, 0, 0, 0.2)';
}, 200);
}
}, 1000);

// Efecto de partículas en el fondo
function createParticle() {
const particle = document.createElement('div');
particle.style.position = 'fixed';
particle.style.width = '2px';
particle.style.height = '2px';
particle.style.backgroundColor = 'rgba(255, 255, 255, 0.5)';
particle.style.borderRadius = '50%';
particle.style.pointerEvents = 'none';
particle.style.left = Math.random() * window.innerWidth + 'px';
particle.style.top = '-2px';
particle.style.zIndex = '-1';

document.body.appendChild(particle);

const animation = particle.animate([
{ transform: 'translateY(0px)', opacity: 1 },
{ transform: `translateY(${window.innerHeight + 10}px)`, opacity: 0 }
], {
duration: Math.random() * 3000 + 2000,
easing: 'linear'
});

animation.onfinish = () => particle.remove();
}

// Crear partículas ocasionalmente
setInterval(createParticle, 300);

// Efectos de sonido simulados con vibración
function playSound(type) {
if (navigator.vibrate) {
switch(type) {
case 'click':
    navigator.vibrate(10);
    break;
case 'error':
    navigator.vibrate([100, 50, 100]);
    break;
case 'equals':
    navigator.vibrate([50, 25, 50]);
    break;
}
}
}

// Agregar efectos de sonido a los botones
document.addEventListener('click', (e) => {
if (e.target.classList.contains('btn')) {
if (e.target.classList.contains('equals')) {
playSound('equals');
} else {
playSound('click');
}
}
});
</script>
</body>
</html>