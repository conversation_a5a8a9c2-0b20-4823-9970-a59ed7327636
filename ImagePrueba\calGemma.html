<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Calculadora Avanzada</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            background-color: #f0f0f0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            margin: 0;
        }

        .calculator {
            background-color: #fff;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.2);
            width: 400px;
            padding: 20px;
            transition: transform 0.3s ease;
        }

        .calculator:hover {
            transform: scale(1.02);
        }

        .display {
            background-color: #eee;
            border-radius: 5px;
            padding: 15px;
            text-align: right;
            font-size: 24px;
            margin-bottom: 10px;
            overflow: hidden; /* Para evitar que el texto se desborde */
            text-overflow: ellipsis; /* Muestra "..." si el texto es demasiado largo */
            white-space: nowrap; /* Evita que el texto se rompa en varias líneas */
        }

        .buttons {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            grid-gap: 5px;
        }

        button {
            background-color: #3498db;
            color: #fff;
            border: none;
            border-radius: 5px;
            padding: 15px;
            font-size: 18px;
            cursor: pointer;
            transition: background-color 0.2s ease;
        }

        button:hover {
            background-color: #2980b9;
        }

        .operator {
            background-color: #e74c3c;
        }

        .operator:hover {
            background-color: #c0392b;
        }

        .clear {
            background-color: #f39c12;
        }

        .clear:hover {
            background-color: #e67e22;
        }

        .equal {
            background-color: #2ecc71;
        }

        .equal:hover {
            background-color: #27ae60;
        }

        .advanced-functions {
            grid-column: 1 / 5;
            display: flex;
            justify-content: space-around;
            margin-bottom: 10px;
        }

        .advanced-functions button {
            padding: 10px;
            font-size: 16px;
            width: 20%;
        }

        /* Animaciones */
        @keyframes buttonPress {
            from {
                transform: scale(1);
            }
            to {
                transform: scale(0.95);
            }
        }

        button:active {
            animation: buttonPress 0.1s ease;
        }
    </style>
</head>
<body>

    <div class="calculator">
        <div class="display" id="display">0</div>

        <div class="advanced-functions">
            <button onclick="calculateSquareRoot()">√</button>
            <button onclick="calculatePower()">x<sup>y</sup></button>
            <button onclick="calculateLogarithm()">log</button>
            <button onclick="calculateFactorial()">!</button>
        </div>

        <div class="buttons">
            <button onclick="clearDisplay()">C</button>
            <button onclick="backspace()">⌫</button>
            <button onclick="appendNumber('%')">%</button>
            <button onclick="appendNumber('/')">/</button>

            <button onclick="appendNumber('7')">7</button>
            <button onclick="appendNumber('8')">8</button>
            <button onclick="appendNumber('9')">9</button>
            <button class="operator" onclick="appendNumber('*')">*</button>

            <button onclick="appendNumber('4')">4</button>
            <button onclick="appendNumber('5')">5</button>
            <button onclick="appendNumber('6')">6</button>
            <button class="operator" onclick="appendNumber('-')">-</button>

            <button onclick="appendNumber('1')">1</button>
            <button onclick="appendNumber('2')">2</button>
            <button onclick="appendNumber('3')">3</button>
            <button onclick="appendOperator('+')">+</button>
            <button onclick="appendOperator('-')">-</button>
            <button onclick="appendOperator('*')">*</button>
            <button onclick="appendOperator('/')">/</button>

            <button onclick="appendNumber('0')">0</button>
            <button onclick="appendNumber('.')">.</button>
            <button class="equal" onclick="calculateResult()">=</button>
        </div>
    </div>

    <script>
        let displayValue = '0';
        let currentOperator = null;
        let firstValue = null;

        function updateDisplay() {
            document.getElementById('display').textContent = displayValue;
        }

        function appendNumber(number) {
            if (displayValue === '0') {
                displayValue = number;
            } else {
                displayValue += number;
            }
            updateDisplay();
        }

        function clearDisplay() {
            displayValue = '0';
            currentOperator = null;
            firstValue = null;
            updateDisplay();
        }

        function backspace() {
            displayValue = displayValue.slice(0, -1);
            if (displayValue === '') {
                displayValue = '0';
            }
            updateDisplay();
        }

        function appendOperator(operator) {
            if (displayValue && displayValue !== 'Error') {
                firstValue = parseFloat(displayValue);
                currentOperator = operator;
                displayValue = '';
                updateDisplay();
                console.log('Operador:', operator, 'FirstValue:', firstValue);
            }
        }

        function calculateResult() {
            const secondValue = parseFloat(displayValue);
            
            if (isNaN(firstValue) || isNaN(secondValue) || !currentOperator) {
                displayValue = 'Error';
                updateDisplay();
                resetCalculator();
                return;
            }
            
            let result;
            switch (currentOperator) {
                case '+':
                    result = firstValue + secondValue;
                    break;
                case '-':
                    result = firstValue - secondValue;
                    break;
                case '*':
                    result = firstValue * secondValue;
                    break;
                case '/':
                    result = secondValue !== 0 ? firstValue / secondValue : 'Error';
                    break;
                case '**':
                    result = Math.pow(firstValue, secondValue);
                    break;
                default:
                    result = 'Error';
            }
            
            displayValue = result.toString();
            updateDisplay();
            firstValue = null;
            currentOperator = null;
        }

        function resetCalculator() {
            firstValue = null;
            currentOperator = null;
            displayValue = '0';
        }

        // Funciones Avanzadas
        function calculateSquareRoot() {
            const num = parseFloat(displayValue);
            if (isNaN(num) || num < 0) {
                displayValue = 'Error';
            } else {
                displayValue = Math.sqrt(num).toString();
            }
            updateDisplay();
        }

        function calculatePower() {
            firstValue = parseFloat(displayValue);
            currentOperator = '**';
            displayValue = '0';
        }

        function calculateLogarithm() {
            const num = parseFloat(displayValue);
            if (isNaN(num) || num <= 0) {
                displayValue = 'Error';
            } else {
                displayValue = Math.log10(num).toString();
            }
            updateDisplay();
        }

        function calculateFactorial() {
            let num = parseFloat(displayValue);
            if (isNaN(num) || num < 0 || !Number.isInteger(num)) {
                displayValue = 'Error';
            } else {
                let result = 1;
                for (let i = 2; i <= num; i++) {
                    result *= i;
                }
                displayValue = result.toString();
            }
            updateDisplay();
        }

        // Manejar el operador de potencia después de ingresar el segundo número
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Enter' && currentOperator === '**') {
                let secondValue = parseFloat(displayValue);
                displayValue = Math.pow(firstValue, secondValue).toString();
                firstValue = null;
                currentOperator = null;
                updateDisplay();
            }
        });

        // Manejar el operador de potencia al presionar el botón "="
        document.getElementById('display').addEventListener('keydown', function(event) {
            if (event.key === 'Enter' && currentOperator === '**') {
                let secondValue = parseFloat(displayValue);
                displayValue = Math.pow(firstValue, secondValue).toString();
                firstValue = null;
                currentOperator = null;
                updateDisplay();
            }
        });
        
        updateDisplay();
    </script>
</body>
</html>
