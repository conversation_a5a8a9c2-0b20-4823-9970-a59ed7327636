:root {
    --primary-color: #2563eb;
    --primary-hover: #1d4ed8;
    --secondary-color: #64748b;
    --accent-color: #f59e0b;
    --background-light: #f8fafc;
    --text-primary: #1e293b;
    --text-secondary: #475569;
    --white: #ffffff;
    --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

/* Header & Navigation */
.header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: var(--white);
    box-shadow: var(--shadow-sm);
    z-index: 1000;
}

.nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 80px;
}

.logo {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
    text-decoration: none;
}

.nav-menu {
    display: flex;
    gap: 2rem;
}

.nav-menu a {
    text-decoration: none;
    color: var(--text-secondary);
    font-weight: 500;
    transition: color 0.3s;
}

.nav-menu a:hover {
    color: var(--primary-color);
}

/* Buttons */
.btn {
    display: inline-block;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    font-weight: 500;
    text-decoration: none;
    transition: all 0.3s;
}

.btn-primary {
    background: var(--primary-color);
    color: var(--white);
}

.btn-primary:hover {
    background: var(--primary-hover);
}

.btn-secondary {
    background: transparent;
    color: var(--text-secondary);
}

.btn-secondary:hover {
    color: var(--primary-color);
}

.btn-outline {
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
    background: transparent;
}

.btn-outline:hover {
    background: var(--primary-color);
    color: var(--white);
}

/* Hero Section */
.hero {
    padding: 160px 0 80px;
    background: var(--background-light);
}

.hero-content {
    max-width: 600px;
    margin-bottom: 3rem;
}

.hero h1 {
    font-size: 3.5rem;
    line-height: 1.2;
    margin-bottom: 1.5rem;
}

.hero p {
    font-size: 1.25rem;
    color: var(--text-secondary);
    margin-bottom: 2rem;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
}

/* Features Section */
.features {
    padding: 100px 0;
}

.features h2 {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 3rem;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.feature-card {
    padding: 2rem;
    background: var(--white);
    border-radius: 1rem;
    box-shadow: var(--shadow-md);
    transition: transform 0.3s;
}

.feature-card:hover {
    transform: translateY(-5px);
}

.feature-icon {
    font-size: 2rem;
    margin-bottom: 1rem;
}

/* Pricing Section */
.pricing {
    padding: 100px 0;
    background: var(--background-light);
}

.pricing h2 {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 3rem;
}

.pricing-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.pricing-card {
    padding: 2rem;
    border-radius: 1rem;
    background-color: var(--white);
    box-shadow: var(--shadow-sm);
    transition: all 0.3s ease;
    text-align: center;
    border: 2px solid transparent;
    position: relative;
    top: 0;
}

.pricing-card:hover {
    border-color: var(--primary-color);
    transform: translateY(-5px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    z-index: 1;
}

.pricing-card.featured {
    border: 2px solid var(--primary-color);
    transform: scale(1.05);
    background-color: var(--white);
    box-shadow: var(--shadow-md);
}

.price {
    font-size: 3rem;
    font-weight: 700;
    margin: 1.5rem 0;
}

.price span {
    font-size: 1.25rem;
    color: var(--text-secondary);
}

.pricing-features {
    list-style: none;
    margin: 2rem 0;
}

.pricing-features li {
    margin: 1rem 0;
    color: var(--text-secondary);
}

/* Testimonials Section */
.testimonials {
    padding: 100px 0;
}

.testimonials h2 {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 3rem;
}

.testimonials-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.testimonial-card {
    background: var(--white);
    border-radius: 1rem;
    padding: 2rem;
    box-shadow: var(--shadow-md);
}

.testimonial-author {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-top: 1.5rem;
}

.testimonial-author img {
    width: 50px;
    height: 50px;
    border-radius: 50%;
}

/* CTA Section */
.cta {
    padding: 100px 0;
    background: var(--primary-color);
    color: var(--white);
    text-align: center;
}

.cta h2 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.cta p {
    font-size: 1.25rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

/* Footer */
.footer {
    padding: 80px 0 40px;
    background: var(--background-light);
}

.footer-grid {
    display: grid;
    grid-template-columns: 2fr repeat(3, 1fr);
    gap: 4rem;
    margin-bottom: 3rem;
}

.footer-brand p {
    color: var(--text-secondary);
    margin-top: 1rem;
}

.footer-links h4 {
    font-size: 1.1rem;
    margin-bottom: 1.5rem;
}

.footer-links a {
    display: block;
    color: var(--text-secondary);
    text-decoration: none;
    margin-bottom: 0.75rem;
    transition: color 0.3s;
}

.footer-links a:hover {
    color: var(--primary-color);
}

.footer-bottom {
    text-align: center;
    color: var(--text-secondary);
    padding-top: 2rem;
    border-top: 1px solid #e2e8f0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-menu {
        display: none;
    }

    .hero h1 {
        font-size: 2.5rem;
    }

    .footer-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .pricing-card.featured {
        transform: none;
    }
}