/* Variables globales */
:root {
    --primary-color: #4A90E2;
    --secondary-color: #50C878;
    --accent-color: #FF6B6B;
    --background-color: #F8F9FA;
    --sidebar-color: #2C3E50;
    --text-primary: #333;
    --text-secondary: #666;
    --shadow-sm: 0 2px 4px rgba(0,0,0,0.1);
    --shadow-md: 0 4px 6px rgba(0,0,0,0.1);
    --shadow-lg: 0 10px 15px rgba(0,0,0,0.1);
    --border-radius: 10px;
    --transition: all 0.3s ease;
}

/* Estilos base */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Poppins', sans-serif;
}

body {
    background-color: var(--background-color);
    color: var(--text-primary);
    line-height: 1.6;
}

/* Layout principal */
.app-container {
    display: grid;
    grid-template-columns: 250px 1fr;
    min-height: 100vh;
}

/* Sidebar */
.sidebar {
    background-color: var(--sidebar-color);
    padding: 1.5rem;
    color: white;
    transition: var(--transition);
}

.sidebar-header {
    margin-bottom: 2rem;
}

.sidebar-header h1 {
    font-size: 1.8rem;
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    margin-bottom: 0.5rem;
}

.sidebar-menu {
    list-style: none;
}

.sidebar-menu li {
    margin-bottom: 0.5rem;
}

.sidebar-menu a {
    display: flex;
    align-items: center;
    padding: 0.8rem 1rem;
    color: white;
    text-decoration: none;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.sidebar-menu a:hover {
    background-color: rgba(255,255,255,0.1);
    transform: translateX(5px);
}

.sidebar-menu .active a {
    background-color: var(--primary-color);
    box-shadow: var(--shadow-md);
}

.sidebar-menu .material-icons {
    margin-right: 1rem;
}

/* Contenido principal */
.main-content {
    padding: 2rem;
    background-color: var(--background-color);
}

/* Header */
.content-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding: 1rem;
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
}

.header-title h2 {
    color: var(--text-primary);
    font-size: 1.8rem;
}

.user-menu {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.user-profile {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background-color: var(--background-color);
    border-radius: 20px;
    cursor: pointer;
    transition: var(--transition);
}

.user-profile:hover {
    background-color: var(--primary-color);
    color: white;
}

/* Dashboard */
.dashboard {
    display: grid;
    gap: 2rem;
}

.dashboard-section {
    background-color: white;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: var(--shadow-sm);
    transition: var(--transition);
}

.dashboard-section:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.section-header h3 {
    color: var(--text-primary);
    font-size: 1.4rem;
}

.view-all {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    transition: var(--transition);
}

.view-all:hover {
    color: var(--secondary-color);
}

/* Botones de acción flotantes */
.action-buttons {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    display: flex;
    gap: 1rem;
    z-index: 1000;
}

.action-button {
    display: flex;
    align-items: center;
    padding: 1rem 1.5rem;
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    color: white;
    border: none;
    border-radius: 30px;
    cursor: pointer;
    font-weight: 600;
    box-shadow: var(--shadow-lg);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.action-button:hover {
    transform: translateY(-3px) scale(1.02);
    box-shadow: 0 15px 20px rgba(0,0,0,0.2);
}

.action-button .material-icons {
    margin-right: 0.5rem;
    font-size: 1.2rem;
}

/* Tarjetas de curso */
.courses-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
}

.course-card {
    background-color: white;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: var(--shadow-sm);
    transition: var(--transition);
    border-top: 4px solid var(--primary-color);
}

.course-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.course-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.course-header h4 {
    color: var(--text-primary);
    font-size: 1.2rem;
}

.course-teacher {
    color: var(--text-secondary);
    margin-bottom: 1rem;
}

.course-footer {
    margin-top: auto;
}

.course-action {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background-color: var(--background-color);
    border: none;
    border-radius: 20px;
    color: var(--text-secondary);
    cursor: pointer;
    transition: var(--transition);
    width: 100%;
}

.course-action:hover {
    background-color: var(--primary-color);
    color: white;
}

/* Lista de actividades */
.activity-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.activity-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background-color: var(--background-color);
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.activity-item:hover {
    transform: translateX(5px);
    background-color: rgba(74, 144, 226, 0.1);
}

.activity-item .material-icons {
    color: var(--primary-color);
}

.activity-content p {
    color: var(--text-primary);
    margin-bottom: 0.25rem;
}

.activity-time {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

/* Animaciones */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.dashboard-section {
    animation: fadeIn 0.5s ease-out;
}

/* Responsive */
@media (max-width: 768px) {
    .app-container {
        grid-template-columns: 1fr;
    }

    .sidebar {
        position: fixed;
        left: -250px;
        top: 0;
        bottom: 0;
        z-index: 100;
    }

    .sidebar.active {
        left: 0;
    }

    .main-content {
        padding: 1rem;
    }

    .courses-grid {
        grid-template-columns: 1fr;
    }
}