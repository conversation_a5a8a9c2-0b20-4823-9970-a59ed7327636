<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tareas - ClassroomP</title>
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <link rel="stylesheet" href="styles/main.css">
</head>
<body>
    <div class="app-container">
        <style>
            .tasks-container {
                padding: 24px;
                background-color: #f5f7fa;
                border-radius: 12px;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            }

            .task-card {
                background: white;
                border-radius: 12px;
                padding: 20px;
                margin-bottom: 16px;
                display: flex;
                gap: 20px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
                transition: transform 0.2s ease, box-shadow 0.2s ease;
            }

            .task-card:hover {
                transform: translateY(-2px);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
            }

            .task-status {
                width: 8px;
                border-radius: 4px;
                background-color: #ffd700;
            }

            .task-status.pending {
                background-color: #ff9800;
            }

            .task-content {
                flex: 1;
            }

            .task-header {
                margin-bottom: 12px;
            }

            .task-header h4 {
                color: #1a237e;
                font-size: 18px;
                margin: 0 0 4px 0;
            }

            .task-course {
                color: #5c6bc0;
                font-size: 14px;
                font-weight: 500;
            }

            .task-description {
                color: #546e7a;
                margin: 0 0 16px 0;
                line-height: 1.5;
            }

            .task-meta {
                display: flex;
                gap: 24px;
            }

            .meta-item {
                display: flex;
                align-items: center;
                gap: 6px;
                color: #78909c;
                font-size: 14px;
            }

            .meta-item .material-icons {
                font-size: 18px;
                color: #90a4ae;
            }

            .task-actions {
                display: flex;
                flex-direction: column;
                gap: 8px;
            }

            .task-action-btn {
                background: none;
                border: none;
                padding: 8px;
                border-radius: 8px;
                cursor: pointer;
                color: #90a4ae;
                transition: all 0.2s ease;
            }

            .task-action-btn:hover {
                background-color: #f5f5f5;
                color: #1976d2;
            }

            .filters-bar {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 24px;
                padding: 16px;
                background: white;
                border-radius: 12px;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            }

            .filter-group {
                display: flex;
                gap: 12px;
            }

            .filter-btn {
                display: flex;
                align-items: center;
                gap: 8px;
                padding: 8px 16px;
                border: 1px solid #e0e0e0;
                border-radius: 8px;
                background: white;
                color: #546e7a;
                cursor: pointer;
                transition: all 0.2s ease;
            }

            .filter-btn:hover {
                background: #f5f5f5;
                border-color: #90a4ae;
            }

            .view-options {
                display: flex;
                gap: 8px;
            }

            .view-btn {
                padding: 8px;
                border: 1px solid #e0e0e0;
                border-radius: 8px;
                background: white;
                color: #90a4ae;
                cursor: pointer;
                transition: all 0.2s ease;
            }

            .view-btn.active {
                background: #e3f2fd;
                color: #1976d2;
                border-color: #1976d2;
            }

            .section-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 20px;
            }

            .section-header h3 {
                color: #1a237e;
                font-size: 20px;
                margin: 0;
            }

            .show-completed-btn {
                display: flex;
                align-items: center;
                gap: 8px;
                background: none;
                border: none;
                color: #1976d2;
                cursor: pointer;
                font-weight: 500;
                padding: 8px 16px;
                border-radius: 8px;
                transition: background-color 0.2s ease;
            }

            .show-completed-btn:hover {
                background-color: #e3f2fd;
            }

            .create-task-btn {
                background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
                color: white;
                border: none;
                padding: 12px 24px;
                border-radius: 8px;
                display: flex;
                align-items: center;
                gap: 8px;
                cursor: pointer;
                font-size: 16px;
                font-weight: 500;
                transition: all 0.3s ease;
                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            }

            .create-task-btn:hover {
                transform: translateY(-2px);
                box-shadow: 0 6px 8px rgba(0, 0, 0, 0.2);
                background: linear-gradient(135deg, #1976D2 0%, #1565C0 100%);
            }
        </style>
        <!-- Barra lateral de navegación -->
        <nav class="sidebar">
            <div class="sidebar-header">
                <h1>ClassroomP</h1>
            </div>
            <ul class="sidebar-menu">
                <li>
                    <a href="index.html">
                        <span class="material-icons">home</span>
                        <span>Inicio</span>
                    </a>
                </li>
                <li>
                    <a href="cursos.html">
                        <span class="material-icons">school</span>
                        <span>Mis Cursos</span>
                    </a>
                </li>
                <li>
                    <a href="calendario.html">
                        <span class="material-icons">calendar_today</span>
                        <span>Calendario</span>
                    </a>
                </li>
                <li class="active">
                    <a href="tareas.html">
                        <span class="material-icons">assignment</span>
                        <span>Tareas</span>
                    </a>
                </li>
                <li>
                    <a href="mensajes.html">
                        <span class="material-icons">message</span>
                        <span>Mensajes</span>
                    </a>
                </li>
            </ul>
        </nav>

        <!-- Contenido principal -->
        <main class="main-content">
            <header class="content-header">
                <div class="header-title">
                    <h2>Tareas</h2>
                </div>
                <div class="header-actions">
                    <div class="user-menu">
                        <span class="material-icons">notifications</span>
                        <div class="user-profile">
                            <span class="material-icons">account_circle</span>
                            <span>Usuario</span>
                        </div>
                    </div>
                </div>
            </header>

            <div class="tasks-container">
                <!-- Filtros y ordenamiento -->
                <div class="filters-bar">
                    <div class="filter-group">
                        <button class="create-task-btn">
                            <span class="material-icons">add</span>
                            <span>Crear tarea</span>
                        </button>
                        <button class="filter-btn">
                            <span class="material-icons">filter_list</span>
                            <span>Curso</span>
                        </button>
                        <button class="filter-btn">
                            <span class="material-icons">date_range</span>
                            <span>Fecha</span>
                        </button>
                        <button class="filter-btn">
                            <span class="material-icons">label</span>
                            <span>Estado</span>
                        </button>
                    </div>
                    <div class="view-options">
                        <button class="view-btn active">
                            <span class="material-icons">view_agenda</span>
                        </button>
                        <button class="view-btn">
                            <span class="material-icons">view_list</span>
                        </button>
                    </div>
                </div>

                <!-- Tareas pendientes -->
                <section class="dashboard-section">
                    <div class="section-header">
                        <h3>Tareas pendientes</h3>
                    </div>
                    <div class="tasks-list">
                        <!-- Tarea 1 -->
                        <div class="task-card">
                            <div class="task-status pending"></div>
                            <div class="task-content">
                                <div class="task-header">
                                    <h4>Ejercicios de Álgebra Lineal</h4>
                                    <span class="task-course">Matemáticas Avanzadas</span>
                                </div>
                                <p class="task-description">Resolver los ejercicios del capítulo 3 sobre matrices y determinantes.</p>
                                <div class="task-meta">
                                    <div class="meta-item">
                                        <span class="material-icons">event</span>
                                        <span>Vence: 15 de marzo, 2024</span>
                                    </div>
                                    <div class="meta-item">
                                        <span class="material-icons">grade</span>
                                        <span>100 puntos</span>
                                    </div>
                                </div>
                            </div>
                            <div class="task-actions">
                                <button class="task-action-btn">
                                    <span class="material-icons">edit</span>
                                </button>
                                <button class="task-action-btn">
                                    <span class="material-icons">delete</span>
                                </button>
                            </div>
                        </div>

                        <!-- Tarea 2 -->
                        <div class="task-card">
                            <div class="task-status pending"></div>
                            <div class="task-content">
                                <div class="task-header">
                                    <h4>Informe de Laboratorio</h4>
                                    <span class="task-course">Ciencias Naturales</span>
                                </div>
                                <p class="task-description">Elaborar el informe del experimento sobre fotosíntesis realizado en clase.</p>
                                <div class="task-meta">
                                    <div class="meta-item">
                                        <span class="material-icons">event</span>
                                        <span>Vence: 18 de marzo, 2024</span>
                                    </div>
                                    <div class="meta-item">
                                        <span class="material-icons">grade</span>
                                        <span>50 puntos</span>
                                    </div>
                                </div>
                            </div>
                            <div class="task-actions">
                                <button class="task-action-btn">
                                    <span class="material-icons">edit</span>
                                </button>
                                <button class="task-action-btn">
                                    <span class="material-icons">delete</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Tareas completadas -->
                <section class="dashboard-section">
                    <div class="section-header">
                        <h3>Tareas completadas</h3>
                        <button class="show-completed-btn">
                            <span class="material-icons">expand_more</span>
                            <span>Mostrar tareas completadas</span>
                        </button>
                    </div>
                </section>
            </div>

        </main>
    </div>
</body>
</html>