body {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    background: linear-gradient(135deg, #2c3e50, #3498db);
    font-family: 'Arial', sans-serif;
    overflow: hidden;
    margin: 0;
}

.calculator {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    animation: fadeIn 1s ease-out;
    transform-style: preserve-3d;
    perspective: 1000px;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-50px); }
    to { opacity: 1; transform: translateY(0); }
}

.display {
    margin-bottom: 20px;
}

#result {
    width: 100%;
    padding: 15px;
    background: rgba(0, 0, 0, 0.3);
    border: none;
    border-radius: 10px;
    font-size: 2.5em;
    color: #fff;
    text-align: right;
    box-sizing: border-box;
    outline: none;
    transition: all 0.3s ease;
    text-shadow: 0 0 5px rgba(255, 255, 255, 0.5);
}

#result:focus {
    box-shadow: 0 0 15px rgba(255, 255, 255, 0.5);
}

.buttons {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    grid-template-rows: repeat(6, 1fr);
    gap: 15px;
}

.btn {
    width: 70px;
    height: 70px;
    border: none;
    border-radius: 15px;
    font-size: 1.8em;
    color: #fff;
    background: rgba(255, 255, 255, 0.1);
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    text-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
    transform: translateZ(0);
}

.btn:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.05) translateZ(10px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
}

.btn:active {
    transform: scale(0.95) translateZ(5px);
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
}

.btn.operator {
    background: rgba(255, 165, 0, 0.7);
}

.btn.operator:hover {
    background: rgba(255, 165, 0, 0.9);
}

.btn.clear {
    background: rgba(255, 0, 0, 0.7);
}

.btn.clear:hover {
    background: rgba(255, 0, 0, 0.9);
}

.btn.equals {
    background: rgba(0, 128, 0, 0.7);
    grid-column: 4 / 5;
    grid-row: 4 / 6;
    height: auto;
}

.btn.mode-toggle {
    background: rgba(50, 50, 50, 0.7);
    grid-column: span 2;
    font-size: 1.2em;
}

.btn.mode-toggle:hover {
    background: rgba(50, 50, 50, 0.9);
}

body.dark-mode {
    background: linear-gradient(135deg, #1a1a2e, #16213e);
}

body.dark-mode .calculator {
    background: rgba(0, 0, 0, 0.3);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

body.dark-mode #result {
    background: rgba(255, 255, 255, 0.1);
    color: #eee;
    text-shadow: 0 0 5px rgba(255, 255, 255, 0.2);
}

body.dark-mode .btn {
    background: rgba(255, 255, 255, 0.05);
    color: #eee;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    text-shadow: 0 0 5px rgba(0, 0, 0, 0.1);
}

body.dark-mode .btn:hover {
    background: rgba(255, 255, 255, 0.1);
}

body.dark-mode .btn.operator {
    background: rgba(255, 165, 0, 0.5);
}

body.dark-mode .btn.operator:hover {
    background: rgba(255, 165, 0, 0.7);
}

body.dark-mode .btn.clear {
    background: rgba(255, 0, 0, 0.5);
}

body.dark-mode .btn.clear:hover {
    background: rgba(255, 0, 0, 0.7);
}

body.dark-mode .btn.equals {
    background: rgba(0, 128, 0, 0.5);
}

body.dark-mode .btn.equals:hover {
    background: rgba(0, 128, 0, 0.7);
}

body.dark-mode .btn.function {
    background: rgba(100, 100, 255, 0.5);
}

body.dark-mode .btn.function:hover {
    background: rgba(100, 100, 255, 0.7);
}

body.dark-mode .btn.mode-toggle {
    background: rgba(80, 80, 80, 0.7);
}

body.dark-mode .btn.mode-toggle:hover {
    background: rgba(80, 80, 80, 0.9);
}

.btn.equals:hover {
    background: rgba(0, 128, 0, 0.9);
}

.btn.function {
    background: rgba(100, 100, 255, 0.7);
    font-size: 1.2em;
}

.btn.function:hover {
    background: rgba(100, 100, 255, 0.9);
}