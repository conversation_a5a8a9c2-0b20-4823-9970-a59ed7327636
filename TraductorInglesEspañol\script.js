document.addEventListener('DOMContentLoaded', () => {
    const dropZone = document.getElementById('drop-zone');
    const fileInput = document.getElementById('file-input');
    const previewImage = document.getElementById('preview-image');
    const originalText = document.getElementById('original-text');
    const translatedText = document.getElementById('translated-text');

    // Configuración de Tesseract.js para OCR
    const worker = Tesseract.createWorker();

    // Inicializar Tesseract
    async function initTesseract() {
        await worker.load();
        await worker.loadLanguage('eng');
        await worker.initialize('eng');
    }
    initTesseract();

    // Función para procesar la imagen
    async function processImage(imageFile) {
        try {
            // Mostrar vista previa de la imagen
            const imageUrl = URL.createObjectURL(imageFile);
            previewImage.src = imageUrl;
            previewImage.style.display = 'block';

            // Extraer texto de la imagen
            const result = await worker.recognize(imageFile);
            originalText.textContent = result.data.text;

            // Traducir el texto usando la API de Google Translate
            const response = await fetch(`https://translate.googleapis.com/translate_a/single?client=gtx&sl=en&tl=es&dt=t&q=${encodeURIComponent(result.data.text)}`);
            const data = await response.json();
            const translation = data[0].map(item => item[0]).join('');
            translatedText.textContent = translation;

        } catch (error) {
            console.error('Error:', error);
            alert('Hubo un error al procesar la imagen. Por favor, intenta de nuevo.');
        }
    }

    // Event Listeners para arrastrar y soltar
    dropZone.addEventListener('dragover', (e) => {
        e.preventDefault();
        dropZone.classList.add('dragover');
    });

    dropZone.addEventListener('dragleave', () => {
        dropZone.classList.remove('dragover');
    });

    dropZone.addEventListener('drop', (e) => {
        e.preventDefault();
        dropZone.classList.remove('dragover');
        const file = e.dataTransfer.files[0];
        if (file && file.type.startsWith('image/')) {
            processImage(file);
        }
    });

    // Event Listener para selección de archivo
    fileInput.addEventListener('change', (e) => {
        const file = e.target.files[0];
        if (file) {
            processImage(file);
        }
    });

    // Event Listener para pegar imagen (Ctrl+V)
    document.addEventListener('paste', (e) => {
        const items = e.clipboardData.items;
        for (let item of items) {
            if (item.type.indexOf('image') === 0) {
                const file = item.getAsFile();
                processImage(file);
                break;
            }
        }
    });

    // Función mejorada para copiar texto
    window.copyText = function(elementId) {
        const textToCopy = document.getElementById(elementId).textContent;
        const textArea = document.createElement('textarea');
        textArea.value = textToCopy;
        document.body.appendChild(textArea);
        textArea.select();
        
        try {
            document.execCommand('copy');
            const button = event.target;
            const originalButtonText = button.textContent;
            button.textContent = '¡Copiado!';
            setTimeout(() => {
                button.textContent = originalButtonText;
            }, 2000);
        } catch (err) {
            console.error('Error al copiar:', err);
        } finally {
            document.body.removeChild(textArea);
        }
    }
});