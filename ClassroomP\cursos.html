<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mis Cursos - ClassroomP</title>
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <link rel="stylesheet" href="styles/main.css">
</head>
<body>
    <div class="app-container">
        <style>
            .courses-container {
                padding: 24px;
                background-color: #f5f7fa;
                border-radius: 12px;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            }

            .course-card {
                background: white;
                border-radius: 12px;
                padding: 20px;
                margin-bottom: 16px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
                transition: transform 0.2s ease, box-shadow 0.2s ease;
            }

            .course-card:hover {
                transform: translateY(-2px);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
            }

            .course-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 12px;
            }

            .course-header h4 {
                color: #1a237e;
                font-size: 18px;
                margin: 0;
            }

            .menu-btn {
                background: none;
                border: none;
                padding: 8px;
                border-radius: 8px;
                cursor: pointer;
                color: #90a4ae;
                transition: all 0.2s ease;
            }

            .menu-btn:hover {
                background-color: #f5f5f5;
                color: #1976d2;
            }

            .course-teacher {
                color: #5c6bc0;
                font-size: 14px;
                font-weight: 500;
                margin: 0 0 12px 0;
            }

            .course-info {
                display: flex;
                gap: 16px;
                margin-bottom: 16px;
            }

            .course-code, .course-period {
                color: #546e7a;
                font-size: 14px;
                padding: 4px 8px;
                background: #f5f7fa;
                border-radius: 4px;
            }

            .course-stats {
                display: flex;
                gap: 24px;
                margin-bottom: 20px;
            }

            .stat-item {
                display: flex;
                align-items: center;
                gap: 6px;
                color: #78909c;
                font-size: 14px;
            }

            .stat-item .material-icons {
                font-size: 18px;
                color: #90a4ae;
            }

            .course-footer {
                display: flex;
                justify-content: flex-end;
            }

            .course-action {
                background: none;
                border: none;
                padding: 8px 16px;
                border-radius: 8px;
                cursor: pointer;
                display: flex;
                align-items: center;
                gap: 8px;
                font-weight: 500;
                transition: all 0.2s ease;
            }

            .course-action.primary {
                background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
                color: white;
                box-shadow: 0 2px 4px rgba(25, 118, 210, 0.2);
            }

            .course-action.primary:hover {
                background: linear-gradient(135deg, #1976D2 0%, #1565C0 100%);
                box-shadow: 0 4px 6px rgba(25, 118, 210, 0.3);
            }

            .section-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 24px;
            }

            .section-header h3 {
                color: #1a237e;
                font-size: 20px;
                margin: 0;
            }

            .section-actions {
                display: flex;
                gap: 12px;
            }

            .filter-btn, .sort-btn {
                display: flex;
                align-items: center;
                gap: 8px;
                padding: 8px 16px;
                border: 1px solid #e0e0e0;
                border-radius: 8px;
                background: white;
                color: #546e7a;
                cursor: pointer;
                transition: all 0.2s ease;
            }

            .filter-btn:hover, .sort-btn:hover {
                background: #f5f5f5;
                border-color: #90a4ae;
            }

            .show-archived-btn {
                display: flex;
                align-items: center;
                gap: 8px;
                background: none;
                border: none;
                color: #1976d2;
                cursor: pointer;
                font-weight: 500;
                padding: 8px 16px;
                border-radius: 8px;
                transition: background-color 0.2s ease;
            }

            .show-archived-btn:hover {
                background-color: #e3f2fd;
            }

            .create-course-btn {
                background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
                color: white;
                border: none;
                padding: 12px 24px;
                border-radius: 8px;
                display: flex;
                align-items: center;
                gap: 8px;
                cursor: pointer;
                font-size: 16px;
                font-weight: 500;
                transition: all 0.3s ease;
                box-shadow: 0 4px 6px rgba(25, 118, 210, 0.2);
            }

            .create-course-btn:hover {
                background: linear-gradient(135deg, #1976D2 0%, #1565C0 100%);
                transform: translateY(-2px);
                box-shadow: 0 6px 8px rgba(25, 118, 210, 0.3);
            }

            .courses-grid {
                display: grid;
                grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
                gap: 24px;
                margin-bottom: 32px;
            }
        </style>
        <!-- Barra lateral de navegación -->
        <nav class="sidebar">
            <div class="sidebar-header">
                <h1>ClassroomP</h1>
            </div>
            <ul class="sidebar-menu">
                <li>
                    <a href="index.html">
                        <span class="material-icons">home</span>
                        <span>Inicio</span>
                    </a>
                </li>
                <li class="active">
                    <a href="cursos.html">
                        <span class="material-icons">school</span>
                        <span>Mis Cursos</span>
                    </a>
                </li>
                <li>
                    <a href="calendario.html">
                        <span class="material-icons">calendar_today</span>
                        <span>Calendario</span>
                    </a>
                </li>
                <li>
                    <a href="tareas.html">
                        <span class="material-icons">assignment</span>
                        <span>Tareas</span>
                    </a>
                </li>
                <li>
                    <a href="mensajes.html">
                        <span class="material-icons">message</span>
                        <span>Mensajes</span>
                    </a>
                </li>
            </ul>
        </nav>

        <!-- Contenido principal -->
        <main class="main-content">
            <header class="content-header">
                <div class="header-title">
                    <h2>Mis Cursos</h2>
                </div>
                <div class="header-actions">
                    <div class="user-menu">
                        <span class="material-icons">notifications</span>
                        <div class="user-profile">
                            <span class="material-icons">account_circle</span>
                            <span>Usuario</span>
                        </div>
                    </div>
                </div>
            </header>

            <div class="courses-container">
                <!-- Cursos activos -->
                <section class="dashboard-section">
                    <div class="section-header">
                        <h3>Cursos activos</h3>
                        <div class="section-actions">
                            <button class="create-course-btn">
                                <span class="material-icons">add</span>
                                <span>Crear curso</span>
                            </button>
                            <button class="filter-btn">
                                <span class="material-icons">filter_list</span>
                                <span>Filtrar</span>
                            </button>
                            <button class="sort-btn">
                                <span class="material-icons">sort</span>
                                <span>Ordenar</span>
                            </button>
                        </div>
                    </div>
                    <div class="courses-grid">
                        <!-- Curso 1 -->
                        <div class="course-card">
                            <div class="course-header">
                                <h4>Matemáticas Avanzadas</h4>
                                <button class="menu-btn">
                                    <span class="material-icons">more_vert</span>
                                </button>
                            </div>
                            <p class="course-teacher">Prof. García</p>
                            <div class="course-info">
                                <span class="course-code">MAT101</span>
                                <span class="course-period">2024-I</span>
                            </div>
                            <div class="course-stats">
                                <div class="stat-item">
                                    <span class="material-icons">people</span>
                                    <span>32 estudiantes</span>
                                </div>
                                <div class="stat-item">
                                    <span class="material-icons">assignment</span>
                                    <span>2 tareas pendientes</span>
                                </div>
                            </div>
                            <div class="course-footer">
                                <button class="course-action primary">
                                    <span>Ir al curso</span>
                                    <span class="material-icons">arrow_forward</span>
                                </button>
                            </div>
                        </div>

                        <!-- Curso 2 -->
                        <div class="course-card">
                            <div class="course-header">
                                <h4>Ciencias Naturales</h4>
                                <button class="menu-btn">
                                    <span class="material-icons">more_vert</span>
                                </button>
                            </div>
                            <p class="course-teacher">Prof. Martínez</p>
                            <div class="course-info">
                                <span class="course-code">CN102</span>
                                <span class="course-period">2024-I</span>
                            </div>
                            <div class="course-stats">
                                <div class="stat-item">
                                    <span class="material-icons">people</span>
                                    <span>28 estudiantes</span>
                                </div>
                                <div class="stat-item">
                                    <span class="material-icons">assignment</span>
                                    <span>1 tarea pendiente</span>
                                </div>
                            </div>
                            <div class="course-footer">
                                <button class="course-action primary">
                                    <span>Ir al curso</span>
                                    <span class="material-icons">arrow_forward</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Cursos archivados -->
                <section class="dashboard-section">
                    <div class="section-header">
                        <h3>Cursos archivados</h3>
                        <button class="show-archived-btn">
                            <span class="material-icons">expand_more</span>
                            <span>Mostrar cursos archivados</span>
                        </button>
                    </div>
                </section>
            </div>
        </main>
    </div>
</body>
</html>