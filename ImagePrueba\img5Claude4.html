<!DOCTYPE html>
<html lang="es">
<head>
   <meta charset="UTF-8">
   <meta name="viewport" content="width=device-width, initial-scale=1.0">
   <title>Gato Gracioso Animado</title>
   <style>
       body {
           margin: 0;
           padding: 0;
           background: linear-gradient(135deg, #87CEEB, #98FB98);
           display: flex;
           justify-content: center;
           align-items: center;
           min-height: 100vh;
           font-family: Arial, sans-serif;
           overflow: hidden;
       }

       .cat-container {
           position: relative;
           transform: scale(0.8);
           animation: float 3s ease-in-out infinite;
       }

       .cat-head {
           width: 200px;
           height: 180px;
           background: #FF8C42;
           border-radius: 50% 50% 45% 45%;
           position: relative;
           box-shadow: inset 0 -20px 30px rgba(0,0,0,0.1);
       }

       .ear {
           width: 40px;
           height: 60px;
           background: #FF8C42;
           border-radius: 50% 50% 0 0;
           position: absolute;
           top: -30px;
           transform-origin: bottom center;
       }

       .ear.left {
           left: 30px;
           transform: rotate(-20deg);
           animation: wiggle-left 2s ease-in-out infinite;
       }

       .ear.right {
           right: 30px;
           transform: rotate(20deg);
           animation: wiggle-right 2s ease-in-out infinite;
       }

       .ear-inner {
           width: 20px;
           height: 30px;
           background: #FFB366;
           border-radius: 50% 50% 0 0;
           position: absolute;
           top: 5px;
           left: 50%;
           transform: translateX(-50%);
       }

       .eye {
           width: 35px;
           height: 45px;
           background: #FFFFFF;
           border-radius: 50%;
           position: absolute;
           top: 50px;
           border: 2px solid #333;
           overflow: hidden;
       }

       .eye.left {
           left: 45px;
           animation: blink 3s ease-in-out infinite;
       }

       .eye.right {
           right: 45px;
           animation: blink 3s ease-in-out infinite 0.1s;
       }

       .pupil {
           width: 15px;
           height: 20px;
           background: #000;
           border-radius: 50%;
           position: absolute;
           top: 12px;
           left: 50%;
           transform: translateX(-50%);
           animation: look-around 4s ease-in-out infinite;
       }

       .highlight {
           width: 6px;
           height: 8px;
           background: #FFFFFF;
           border-radius: 50%;
           position: absolute;
           top: 2px;
           left: 2px;
       }

       .nose {
           width: 15px;
           height: 12px;
           background: #FF69B4;
           border-radius: 50% 50% 50% 50%;
           position: absolute;
           top: 95px;
           left: 50%;
           transform: translateX(-50%);
           box-shadow: 0 2px 4px rgba(0,0,0,0.2);
       }

       .mouth {
           position: absolute;
           top: 110px;
           left: 50%;
           transform: translateX(-50%);
       }

       .mouth-line {
           width: 30px;
           height: 2px;
           background: #333;
           border-radius: 2px;
           position: absolute;
           top: 0;
       }

       .mouth-line.left {
           left: -15px;
           transform: rotate(15deg);
       }

       .mouth-line.right {
           right: -15px;
           transform: rotate(-15deg);
       }

       .whisker {
           width: 40px;
           height: 2px;
           background: #333;
           position: absolute;
           top: 80px;
           border-radius: 2px;
           animation: whisker-twitch 2s ease-in-out infinite;
       }

       .whisker.left-1 {
           left: -45px;
           transform: rotate(-10deg);
       }

       .whisker.left-2 {
           left: -40px;
           top: 95px;
           transform: rotate(5deg);
       }

       .whisker.right-1 {
           right: -45px;
           transform: rotate(10deg);
       }

       .whisker.right-2 {
           right: -40px;
           top: 95px;
           transform: rotate(-5deg);
       }

       .cheek {
           width: 30px;
           height: 25px;
           background: #FFB366;
           border-radius: 50%;
           position: absolute;
           top: 85px;
           opacity: 0.7;
       }

       .cheek.left {
           left: 10px;
       }

       .cheek.right {
           right: 10px;
       }

       .tongue {
           width: 20px;
           height: 15px;
           background: #FF1493;
           border-radius: 0 0 50% 50%;
           position: absolute;
           top: 125px;
           left: 50%;
           transform: translateX(-50%);
           animation: tongue-out 4s ease-in-out infinite;
           opacity: 0;
       }

       .stripe {
           position: absolute;
           background: #E6722C;
           border-radius: 10px;
       }

       .stripe-1 {
           width: 60px;
           height: 8px;
           top: 30px;
           left: 70px;
           transform: rotate(-15deg);
       }

       .stripe-2 {
           width: 40px;
           height: 6px;
           top: 60px;
           right: 40px;
           transform: rotate(20deg);
       }

       .stripe-3 {
           width: 50px;
           height: 7px;
           top: 120px;
           left: 50px;
           transform: rotate(-10deg);
       }

       @keyframes float {
           0%, 100% { transform: translateY(0px) scale(0.8); }
           50% { transform: translateY(-10px) scale(0.8); }
       }

       @keyframes blink {
           0%, 90%, 100% { height: 45px; }
           95% { height: 5px; }
       }

       @keyframes wiggle-left {
           0%, 100% { transform: rotate(-20deg); }
           50% { transform: rotate(-25deg); }
       }

       @keyframes wiggle-right {
           0%, 100% { transform: rotate(20deg); }
           50% { transform: rotate(25deg); }
       }

       @keyframes look-around {
           0%, 100% { left: 50%; }
           25% { left: 60%; }
           75% { left: 40%; }
       }

       @keyframes whisker-twitch {
           0%, 100% { transform: rotate(var(--rotation, 0deg)); }
           50% { transform: rotate(calc(var(--rotation, 0deg) + 5deg)); }
       }

       @keyframes tongue-out {
           0%, 70%, 100% { opacity: 0; transform: translateX(-50%) translateY(0); }
           75%, 95% { opacity: 1; transform: translateX(-50%) translateY(5px); }
       }

       .paw-print {
           position: absolute;
           width: 30px;
           height: 30px;
           animation: fade-in-out 2s ease-in-out infinite;
       }

       .paw-print:nth-child(1) {
           top: 20%;
           left: 20%;
           animation-delay: 0s;
       }

       .paw-print:nth-child(2) {
           top: 60%;
           right: 15%;
           animation-delay: 1s;
       }

       .paw-print:nth-child(3) {
           bottom: 30%;
           left: 10%;
           animation-delay: 2s;
       }

       @keyframes fade-in-out {
           0%, 100% { opacity: 0; transform: scale(0); }
           50% { opacity: 0.3; transform: scale(1); }
       }

       .heart {
           position: absolute;
           width: 20px;
           height: 20px;
           background: #FF69B4;
           transform: rotate(45deg);
           animation: hearts 3s ease-in-out infinite;
           opacity: 0;
       }

       .heart:before,
       .heart:after {
           content: '';
           width: 20px;
           height: 20px;
           position: absolute;
           left: -10px;
           background: #FF69B4;
           border-radius: 50%;
       }

       .heart:after {
           top: -10px;
           left: 0;
       }

       .heart-1 {
           top: 10%;
           right: 20%;
           animation-delay: 0.5s;
       }

       .heart-2 {
           top: 30%;
           left: 15%;
           animation-delay: 1.5s;
       }

       @keyframes hearts {
           0%, 90%, 100% { opacity: 0; transform: rotate(45deg) translateY(0); }
           10%, 80% { opacity: 0.8; transform: rotate(45deg) translateY(-20px); }
       }

       .whisker.left-1 { --rotation: -10deg; }
       .whisker.left-2 { --rotation: 5deg; }
       .whisker.right-1 { --rotation: 10deg; }
       .whisker.right-2 { --rotation: -5deg; }
   </style>
</head>
<body>
   <div class="paw-print">🐾</div>
   <div class="paw-print">🐾</div>
   <div class="paw-print">🐾</div>
   
   <div class="heart heart-1"></div>
   <div class="heart heart-2"></div>
   
   <div class="cat-container">
       <div class="cat-head">
           <!-- Orejas -->
           <div class="ear left">
               <div class="ear-inner"></div>
           </div>
           <div class="ear right">
               <div class="ear-inner"></div>
           </div>
           
           <!-- Rayas -->
           <div class="stripe stripe-1"></div>
           <div class="stripe stripe-2"></div>
           <div class="stripe stripe-3"></div>
           
           <!-- Ojos -->
           <div class="eye left">
               <div class="pupil">
                   <div class="highlight"></div>
               </div>
           </div>
           <div class="eye right">
               <div class="pupil">
                   <div class="highlight"></div>
               </div>
           </div>
           
           <!-- Mejillas -->
           <div class="cheek left"></div>
           <div class="cheek right"></div>
           
           <!-- Bigotes -->
           <div class="whisker left-1"></div>
           <div class="whisker left-2"></div>
           <div class="whisker right-1"></div>
           <div class="whisker right-2"></div>
           
           <!-- Nariz -->
           <div class="nose"></div>
           
           <!-- Boca -->
           <div class="mouth">
               <div class="mouth-line left"></div>
               <div class="mouth-line right"></div>
           </div>
           
           <!-- Lengua -->
           <div class="tongue"></div>
       </div>
   </div>

   <script>
       // Animación interactiva al hacer clic
       document.addEventListener('click', function(e) {
           const cat = document.querySelector('.cat-container');
           cat.style.animation = 'none';
           cat.style.transform = 'scale(0.9) rotate(5deg)';
           
           setTimeout(() => {
               cat.style.animation = 'float 3s ease-in-out infinite';
               cat.style.transform = 'scale(0.8)';
           }, 200);
           
           // Crear corazones en la posición del clic
           createHeart(e.clientX, e.clientY);
       });

       function createHeart(x, y) {
           const heart = document.createElement('div');
           heart.innerHTML = '💖';
           heart.style.position = 'absolute';
           heart.style.left = x + 'px';
           heart.style.top = y + 'px';
           heart.style.fontSize = '20px';
           heart.style.pointerEvents = 'none';
           heart.style.animation = 'hearts 2s ease-out forwards';
           document.body.appendChild(heart);
           
           setTimeout(() => {
               heart.remove();
           }, 2000);
       }

       // Animación de seguimiento del mouse para los ojos
       document.addEventListener('mousemove', function(e) {
           const pupils = document.querySelectorAll('.pupil');
           const rect = document.querySelector('.cat-head').getBoundingClientRect();
           const centerX = rect.left + rect.width / 2;
           const centerY = rect.top + rect.height / 2;
           
           const mouseX = e.clientX;
           const mouseY = e.clientY;
           
           const angle = Math.atan2(mouseY - centerY, mouseX - centerX);
           const distance = Math.min(Math.sqrt(Math.pow(mouseX - centerX, 2) + Math.pow(mouseY - centerY, 2)) / 10, 5);
           
           pupils.forEach(pupil => {
               const offsetX = Math.cos(angle) * distance;
               const offsetY = Math.sin(angle) * distance;
               pupil.style.transform = `translate(${offsetX}px, ${offsetY}px)`;
           });
       });

       // Sonidos simulados con vibración en móviles
       function playMeow() {
           if (navigator.vibrate) {
               navigator.vibrate([100, 50, 100]);
           }
       }

       // Maullar al tocar la nariz
       document.querySelector('.nose').addEventListener('click', function(e) {
           e.stopPropagation();
           playMeow();
           this.style.transform = 'translateX(-50%) scale(1.2)';
           setTimeout(() => {
               this.style.transform = 'translateX(-50%) scale(1)';
           }, 150);
       });

       // Efecto de parpadeo aleatorio adicional
       setInterval(() => {
           if (Math.random() > 0.7) {
               const eyes = document.querySelectorAll('.eye');
               eyes.forEach(eye => {
                   eye.style.animation = 'none';
                   eye.style.height = '5px';
                   setTimeout(() => {
                       eye.style.height = '45px';
                       setTimeout(() => {
                           eye.style.animation = 'blink 3s ease-in-out infinite';
                       }, 100);
                   }, 100);
               });
           }
       }, 3000);
   </script>
</body>
</html>