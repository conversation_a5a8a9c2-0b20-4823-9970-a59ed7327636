<!DOCTYPE html>
<html>
<head>
  <style>
    .character {
      position: relative;
      width: 200px;
      height: 250px;
      background: #f4a261;
      border-radius: 50%;
      margin: 50px auto;
    }
    .head {
      position: absolute;
      top: -50px;
      left: 50px;
      width: 100px;
      height: 100px;
      background: #f4a261;
      border-radius: 50%;
    }
    .face {
      position: absolute;
      top: 20px;
      left: 20px;
      width: 60px;
      height: 60px;
      background: #ffe8d6;
      border-radius: 50%;
    }
    .eye-left, .eye-right {
      position: absolute;
      width: 10px;
      height: 10px;
      background: black;
      border-radius: 50%;
      top: 15px;
    }
    .eye-left { left: 15px; }
    .eye-right { right: 15px; }
    .nose {
      position: absolute;
      width: 10px;
      height: 10px;
      background: black;
      border-radius: 50%;
      top: 30px;
      left: 25px;
    }
    .mouth {
      position: absolute;
      width: 20px;
      height: 10px;
      border-bottom: 2px solid black;
      border-radius: 0 0 10px 10px;
      top: 40px;
      left: 20px;
    }
    .arm-left {
      position: absolute;
      top: 80px;
      left: -20px;
      width: 40px;
      height: 80px;
      background: #f4a261;
      border-radius: 20px;
      transform-origin: bottom;
      animation: wave 2s infinite;
    }
    .arm-right {
      position: absolute;
      top: 80px;
      right: -20px;
      width: 40px;
      height: 80px;
      background: #f4a261;
      border-radius: 20px;
    }
    .belly {
      position: absolute;
      top: 120px;
      left: 50px;
      width: 100px;
      height: 100px;
      background: #ffe8d6;
      border-radius: 50%;
    }
    .x-mark {
      position: absolute;
      top: 150px;
      left: 80px;
      width: 20px;
      height: 20px;
      border: 2px solid black;
      transform: rotate(45deg);
    }
    .x-mark::before, .x-mark::after {
      content: '';
      position: absolute;
      width: 20px;
      height: 2px;
      background: black;
    }
    .x-mark::before { transform: rotate(45deg); }
    .x-mark::after { transform: rotate(-45deg); }
    .tail {
      position: absolute;
      top: 150px;
      right: -40px;
      width: 60px;
      height: 40px;
      background: #f4a261;
      border-radius: 20px;
      transform: rotate(30deg);
    }
    @keyframes wave {
      0% { transform: rotate(0deg); }
      25% { transform: rotate(30deg); }
      50% { transform: rotate(0deg); }
      75% { transform: rotate(-30deg); }
      100% { transform: rotate(0deg); }
    }
  </style>
</head>
<body>
  <div class="character">
    <div class="head">
      <div class="face">
        <div class="eye-left"></div>
        <div class="eye-right"></div>
        <div class="nose"></div>
        <div class="mouth"></div>
      </div>
    </div>
    <div class="arm-left"></div>
    <div class="arm-right"></div>
    <div class="belly">
      <div class="x-mark"></div>
    </div>
    <div class="tail"></div>
  </div>
</body>
</html>