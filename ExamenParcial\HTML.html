<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Registro de Notas</title>
    <!-- Enlace a Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            padding-top: 70px; /* Ajuste para la barra de navegación fija */
            background-color: #f8f9fa;
        }
        .nav-custom {
            background-color: #b3544b;
            border-radius: 0 0 8px 8px;
            margin: 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1030;
            width: 100%;
            display: flex;
            justify-content: center;
            min-height: 48px;
        }
        .nav-custom .nav-link {
            color: #fff !important;
            font-weight: bold;
            text-decoration: none;
            margin: 0 15px;
            border-radius: 6px;
            transition: background 0.3s, color 0.3s, transform 0.2s;
            position: relative;
        }
        .nav-custom .nav-link.active {
            color: #fff !important;
            background: #2563eb;
            text-decoration: underline;
            transform: scale(1.1);
            box-shadow: 0 2px 8px rgba(37,99,235,0.15);
        }
        .nav-custom .nav-link:hover:not(.active) {
            background: #fff2;
            color: #ffe;
            transform: translateY(-2px) scale(1.07);
            box-shadow: 0 2px 8px rgba(255,255,255,0.08);
        }
        .card-custom {
            border: 1px solid #ff9c2a;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 30px;
            background-color: #fff;
            box-shadow: 0 2px 6px rgba(0,0,0,0.1);
        }
        .table-custom th {
            background-color: #b3544b;
            color: white;
        }
        .table-custom tbody tr:nth-child(odd) {
            background-color: #f2f2f2;
        }
        .table-custom td {
            vertical-align: middle;
        }
    </style>
</head>
<body>

    <nav class="nav nav-pills flex-row nav-custom">
        <a class="nav-link" href="INICIO.html">INICIO</a>
        <a class="nav-link active" href="#">HTML</a>
        <a class="nav-link" href="CSS.html">CSS</a>
        <a class="nav-link" href="BOOTSTRAP.html">BOOSTSTRAP</a>
        <a class="nav-link" href="CONTACTO.html">CONTACTO</a>
    </nav>

    <div class="container mt-4">
        <div class="card-custom">
            <h5 class="card-title text-center mb-4">Calificación del Alumno</h5>
            <form>
                <div class="row mb-3">
                    <label for="apellidosNombres" class="col-sm-3 col-form-label">Apellidos y Nombres</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" id="apellidosNombres">
                    </div>
                </div>
                <div class="row mb-3">
                    <label for="curso" class="col-sm-3 col-form-label">Curso</label>
                    <div class="col-sm-9">
                        <select class="form-select" id="curso">
                            <option selected>Seleccione un curso</option>
                            <option value="1">Curso 1</option>
                            <option value="2">Curso 2</option>
                            <option value="3">Curso 3</option>
                        </select>
                    </div>
                </div>
                <div class="row mb-3">
                    <label class="col-sm-3 col-form-label">Ciclo</label>
                    <div class="col-sm-9 d-flex align-items-center">
                        <div class="form-check form-check-inline">
                            <input class="form-check-input" type="radio" name="ciclo" id="ciclo1" value="1">
                            <label class="form-check-label" for="ciclo1">O</label>
                        </div>
                        <div class="form-check form-check-inline">
                            <input class="form-check-input" type="radio" name="ciclo" id="ciclo2" value="2">
                            <label class="form-check-label" for="ciclo2">O</label>
                        </div>
                        <div class="form-check form-check-inline">
                            <input class="form-check-input" type="radio" name="ciclo" id="ciclo3" value="3">
                            <label class="form-check-label" for="ciclo3">O</label>
                        </div>
                    </div>
                </div>
                <div class="row mb-3">
                    <label for="nota01" class="col-sm-3 col-form-label">Nota 01:</label>
                    <div class="col-sm-3">
                        <input type="text" class="form-control" id="nota01">
                    </div>
                    <label for="nota02" class="col-sm-3 col-form-label">Nota 02:</label>
                    <div class="col-sm-3">
                        <input type="text" class="form-control" id="nota02">
                    </div>
                </div>
                 <div class="row mb-3">
                    <div class="col-sm-3"></div> <!-- Espacio para alinear Nota 03 -->
                    <label for="nota03" class="col-sm-3 col-form-label">Nota 03:</label>
                    <div class="col-sm-3">
                        <input type="text" class="form-control" id="nota03">
                    </div>
                     <div class="col-sm-3"></div> <!-- Espacio para alinear Nota 03 -->
                </div>
                <div class="row">
                    <div class="col-12">
                        <button type="submit" class="btn btn-danger">REGISTRAR</button>
                    </div>
                </div>
            </form>
        </div>

        <h5 class="text-center mb-3">REGISTRO DE NOTAS</h5>

        <table class="table table-bordered table-custom">
            <thead>
                <tr>
                    <th>Alumno</th>
                    <th>Nota 01</th>
                    <th>Nota 02</th>
                    <th>Nota 03</th>
                    <th>Promedio</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>Alumno 01</td>
                    <td>14</td>
                    <td>13</td>
                    <td>16</td>
                    <td>15</td>
                </tr>
                <tr>
                    <td>Alumno 02</td>
                    <td></td>
                    <td>16</td>
                    <td>18</td>
                    <td>17</td>
                </tr>
                <tr>
                    <td>Alumno 03</td>
                    <td>14</td>
                    <td>20</td>
                    <td>17</td>
                    <td>17</td>
                </tr>
                <tr>
                    <td>Alumno 04</td>
                    <td>10</td>
                    <td></td>
                    <td>13</td>
                    <td>19</td>
                </tr>
                 <tr>
                    <td>Alumno 05</td>
                    <td>13</td>
                    <td>17</td>
                    <td></td>
                    <td>15</td>
                </tr>
            </tbody>
        </table>

    </div>

    <!-- Enlace a Bootstrap JS (opcional, si necesitas funcionalidades de JS de Bootstrap) -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>