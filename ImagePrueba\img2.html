<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gato CSS</title>
    <style>
        body {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            background-color: #f0f0f0;
            margin: 0;
            font-family: Arial, sans-serif;
        }

        .cat-container {
            position: relative;
            width: 300px;
            height: 300px;
        }

        /* Cabeza del gato */
        .cat-head {
            position: absolute;
            width: 200px;
            height: 180px;
            background-color: #a8a8a8;
            border-radius: 50% 50% 40% 40%;
            top: 50px;
            left: 50px;
            box-shadow: 0 0 15px rgba(0, 0, 0, 0.2);
            overflow: hidden;
        }

        /* Orejas */
        .ear {
            position: absolute;
            width: 0;
            height: 0;
            border-left: 30px solid transparent;
            border-right: 30px solid transparent;
            border-bottom: 60px solid #a8a8a8;
        }

        .ear-left {
            transform: rotate(-30deg);
            top: 10px;
            left: 30px;
        }

        .ear-right {
            transform: rotate(30deg);
            top: 10px;
            right: 30px;
        }

        /* Ojos */
        .eye {
            position: absolute;
            width: 45px;
            height: 45px;
            background-color: #fff;
            border-radius: 50%;
            top: 80px;
        }

        .eye-left {
            left: 50px;
        }

        .eye-right {
            right: 50px;
        }

        /* Pupilas */
        .pupil {
            position: absolute;
            width: 25px;
            height: 35px;
            background-color: #daa520;
            border-radius: 50%;
            top: 5px;
            left: 10px;
        }

        .pupil::after {
            content: '';
            position: absolute;
            width: 10px;
            height: 15px;
            background-color: #000;
            border-radius: 50%;
            top: 10px;
            left: 7px;
        }

        /* Nariz */
        .nose {
            position: absolute;
            width: 20px;
            height: 12px;
            background-color: #ffb6c1;
            border-radius: 50%;
            top: 130px;
            left: 90px;
        }

        /* Boca y lengua */
        .mouth {
            position: absolute;
            width: 40px;
            height: 20px;
            background-color: #ffb6c1;
            border-radius: 0 0 20px 20px;
            top: 142px;
            left: 80px;
            overflow: hidden;
        }

        .tongue {
            position: absolute;
            width: 30px;
            height: 40px;
            background-color: #ff6b81;
            border-radius: 15px;
            top: 0;
            left: 5px;
        }

        /* Bigotes */
        .whisker {
            position: absolute;
            width: 60px;
            height: 2px;
            background-color: #fff;
            top: 135px;
        }

        .whisker-1 {
            transform: rotate(-10deg);
            left: 10px;
        }

        .whisker-2 {
            transform: rotate(10deg);
            left: 10px;
            top: 145px;
        }

        .whisker-3 {
            transform: rotate(-10deg);
            left: 10px;
            top: 125px;
        }

        .whisker-4 {
            transform: rotate(10deg);
            right: 10px;
        }

        .whisker-5 {
            transform: rotate(-10deg);
            right: 10px;
            top: 145px;
        }

        .whisker-6 {
            transform: rotate(10deg);
            right: 10px;
            top: 125px;
        }

        /* Pecho blanco */
        .chest {
            position: absolute;
            width: 100px;
            height: 80px;
            background-color: #fff;
            border-radius: 50%;
            bottom: 0;
            left: 50px;
        }

        /* Patas */
        .paw {
            position: absolute;
            width: 40px;
            height: 30px;
            background-color: #a8a8a8;
            border-radius: 50%;
            bottom: 20px;
        }

        .paw-left {
            left: 60px;
        }

        .paw-right {
            right: 60px;
        }

        /* Almohadillas */
        .pad {
            position: absolute;
            width: 15px;
            height: 10px;
            background-color: #ffb6c1;
            border-radius: 50%;
            bottom: 5px;
        }

        .pad-left {
            left: 12px;
        }

        .pad-right {
            right: 12px;
        }

        /* Fondo de la cama */
        .bed {
            position: absolute;
            width: 300px;
            height: 100px;
            background-color: #c5e0dc;
            border-radius: 20px;
            bottom: 0;
            z-index: -1;
        }

        /* Manchas en el pelaje */
        .spot {
            position: absolute;
            background-color: #8a7f7f;
            border-radius: 50%;
        }

        .spot-1 {
            width: 40px;
            height: 30px;
            top: 70px;
            left: 30px;
        }

        .spot-2 {
            width: 30px;
            height: 25px;
            top: 100px;
            right: 40px;
        }

        .spot-3 {
            width: 25px;
            height: 20px;
            top: 130px;
            left: 50px;
        }
    </style>
</head>
<body>
    <div class="cat-container">
        <div class="bed"></div>
        <div class="cat-head">
            <div class="spot spot-1"></div>
            <div class="spot spot-2"></div>
            <div class="spot spot-3"></div>
            <div class="chest"></div>
        </div>
        <div class="ear ear-left"></div>
        <div class="ear ear-right"></div>
        <div class="eye eye-left">
            <div class="pupil"></div>
        </div>
        <div class="eye eye-right">
            <div class="pupil"></div>
        </div>
        <div class="nose"></div>
        <div class="mouth">
            <div class="tongue"></div>
        </div>
        <div class="whisker whisker-1"></div>
        <div class="whisker whisker-2"></div>
        <div class="whisker whisker-3"></div>
        <div class="whisker whisker-4"></div>
        <div class="whisker whisker-5"></div>
        <div class="whisker whisker-6"></div>
        <div class="paw paw-left">
            <div class="pad pad-left"></div>
        </div>
        <div class="paw paw-right">
            <div class="pad pad-right"></div>
        </div>
    </div>
</body>
</html>