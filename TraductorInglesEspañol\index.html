<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Traductor de Imágenes</title>
    <link rel="stylesheet" href="styles.css">
    <script src='https://unpkg.com/tesseract.js@v2.1.0/dist/tesseract.min.js'></script>
</head>
<body>
    <div class="container">
        <h1>Traductor de Imágenes</h1>
        
        <div class="upload-section">
            <div id="drop-zone" class="drop-zone">
                <p>Arrastra y suelta una imagen aquí o</p>
                <input type="file" id="file-input" accept="image/*">
                <label for="file-input" class="upload-button">Selecciona un archivo</label>
                <p>También puedes pegar (Ctrl+V) una imagen directamente</p>
            </div>
        </div>

        <div class="preview-section">
            <img id="preview-image" style="display: none;">
        </div>

        <div class="result-section">
            <div class="text-box">
                <div class="text-header">
                    <h3>Texto Original (Inglés)</h3>
                    <button class="copy-button" onclick="copyText('original-text')">Copiar</button>
                </div>
                <div id="original-text" class="text-content"></div>
            </div>
            <div class="text-box">
                <div class="text-header">
                    <h3>Texto Traducido (Español)</h3>
                    <button class="copy-button" onclick="copyText('translated-text')">Copiar</button>
                </div>
                <div id="translated-text" class="text-content"></div>
            </div>
        </div>
    </div>
    <script src="script.js"></script>
</body>
</html>