<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Calculadora Avanzada</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
        }

        .calculator {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
            border: 1px solid rgba(255, 255, 255, 0.18);
            max-width: 400px;
            width: 100%;
        }

        .display {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            min-height: 80px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: flex-end;
        }

        .display-history {
            color: rgba(255, 255, 255, 0.7);
            font-size: 14px;
            margin-bottom: 5px;
            min-height: 20px;
        }

        .display-current {
            color: white;
            font-size: 2.5em;
            font-weight: bold;
            word-wrap: break-word;
            text-align: right;
        }

        .buttons {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 15px;
        }

        .btn {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            border-radius: 15px;
            padding: 20px;
            font-size: 1.2em;
            font-weight: bold;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .btn:active {
            transform: translateY(0);
        }

        .btn-operator {
            background: rgba(255, 165, 0, 0.7);
        }

        .btn-operator:hover {
            background: rgba(255, 165, 0, 0.9);
        }

        .btn-equals {
            background: rgba(0, 255, 127, 0.7);
            grid-column: span 2;
        }

        .btn-equals:hover {
            background: rgba(0, 255, 127, 0.9);
        }

        .btn-clear {
            background: rgba(255, 69, 58, 0.7);
        }

        .btn-clear:hover {
            background: rgba(255, 69, 58, 0.9);
        }

        .btn-function {
            background: rgba(138, 43, 226, 0.7);
            font-size: 0.9em;
        }

        .btn-function:hover {
            background: rgba(138, 43, 226, 0.9);
        }

        .btn-zero {
            grid-column: span 2;
        }

        .mode-toggle {
            display: flex;
            justify-content: center;
            margin-bottom: 20px;
        }

        .mode-btn {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            border-radius: 10px;
            padding: 10px 20px;
            color: white;
            cursor: pointer;
            margin: 0 5px;
            transition: all 0.3s ease;
        }

        .mode-btn.active {
            background: rgba(255, 255, 255, 0.4);
        }

        .scientific-panel {
            display: none;
            grid-template-columns: repeat(4, 1fr);
            gap: 10px;
            margin-bottom: 15px;
        }

        .scientific-panel.active {
            display: grid;
        }

        .memory-panel {
            display: flex;
            justify-content: space-between;
            margin-bottom: 15px;
            gap: 10px;
        }

        .memory-btn {
            background: rgba(75, 0, 130, 0.7);
            border: none;
            border-radius: 10px;
            padding: 8px 12px;
            color: white;
            cursor: pointer;
            font-size: 0.9em;
            flex: 1;
            transition: all 0.3s ease;
        }

        .memory-btn:hover {
            background: rgba(75, 0, 130, 0.9);
        }

        @media (max-width: 480px) {
            .calculator {
                padding: 20px;
            }
            
            .btn {
                padding: 15px;
                font-size: 1em;
            }
            
            .display-current {
                font-size: 2em;
            }
        }
    </style>
</head>
<body>
    <div class="calculator">
        <div class="mode-toggle">
            <button class="mode-btn active" onclick="toggleMode('basic')">Básica</button>
            <button class="mode-btn" onclick="toggleMode('scientific')">Científica</button>
        </div>

        <div class="memory-panel">
            <button class="memory-btn" onclick="memoryStore()">MS</button>
            <button class="memory-btn" onclick="memoryRecall()">MR</button>
            <button class="memory-btn" onclick="memoryAdd()">M+</button>
            <button class="memory-btn" onclick="memorySubtract()">M-</button>
            <button class="memory-btn" onclick="memoryClear()">MC</button>
        </div>

        <div class="display">
            <div class="display-history" id="history"></div>
            <div class="display-current" id="display">0</div>
        </div>

        <div class="scientific-panel" id="scientificPanel">
            <button class="btn btn-function" onclick="scientificFunction('sin')">sin</button>
            <button class="btn btn-function" onclick="scientificFunction('cos')">cos</button>
            <button class="btn btn-function" onclick="scientificFunction('tan')">tan</button>
            <button class="btn btn-function" onclick="scientificFunction('log')">log</button>
            <button class="btn btn-function" onclick="scientificFunction('ln')">ln</button>
            <button class="btn btn-function" onclick="scientificFunction('sqrt')">√</button>
            <button class="btn btn-function" onclick="scientificFunction('pow')">x²</button>
            <button class="btn btn-function" onclick="scientificFunction('factorial')">x!</button>
            <button class="btn btn-function" onclick="scientificFunction('pi')">π</button>
            <button class="btn btn-function" onclick="scientificFunction('e')">e</button>
            <button class="btn btn-function" onclick="scientificFunction('abs')">|x|</button>
            <button class="btn btn-function" onclick="scientificFunction('1/x')">1/x</button>
        </div>

        <div class="buttons">
            <button class="btn btn-clear" onclick="clearAll()">C</button>
            <button class="btn btn-clear" onclick="clearEntry()">CE</button>
            <button class="btn btn-operator" onclick="appendOperator('/')" title="División">÷</button>
            <button class="btn btn-operator" onclick="backspace()">⌫</button>

            <button class="btn" onclick="appendNumber('7')">7</button>
            <button class="btn" onclick="appendNumber('8')">8</button>
            <button class="btn" onclick="appendNumber('9')">9</button>
            <button class="btn btn-operator" onclick="appendOperator('*')" title="Multiplicación">×</button>

            <button class="btn" onclick="appendNumber('4')">4</button>
            <button class="btn" onclick="appendNumber('5')">5</button>
            <button class="btn" onclick="appendNumber('6')">6</button>
            <button class="btn btn-operator" onclick="appendOperator('-')" title="Resta">-</button>

            <button class="btn" onclick="appendNumber('1')">1</button>
            <button class="btn" onclick="appendNumber('2')">2</button>
            <button class="btn" onclick="appendNumber('3')">3</button>
            <button class="btn btn-operator" onclick="appendOperator('+')" title="Suma">+</button>

            <button class="btn btn-zero" onclick="appendNumber('0')">0</button>
            <button class="btn" onclick="appendDecimal()">.</button>
            <button class="btn btn-equals" onclick="calculate()">=</button>
        </div>
    </div>

    <script>
        let currentInput = '0';
        let previousInput = '';
        let operator = '';
        let waitingForOperand = false;
        let memory = 0;
        let history = '';
        let isScientificMode = false;

        const display = document.getElementById('display');
        const historyDisplay = document.getElementById('history');

        function updateDisplay() {
            display.textContent = currentInput;
            historyDisplay.textContent = history;
        }

        function appendNumber(number) {
            if (waitingForOperand) {
                currentInput = number;
                waitingForOperand = false;
            } else {
                currentInput = currentInput === '0' ? number : currentInput + number;
            }
            updateDisplay();
        }

        function appendDecimal() {
            if (waitingForOperand) {
                currentInput = '0.';
                waitingForOperand = false;
            } else if (currentInput.indexOf('.') === -1) {
                currentInput += '.';
            }
            updateDisplay();
        }

        function appendOperator(nextOperator) {
            const inputValue = parseFloat(currentInput);

            if (previousInput === '') {
                previousInput = inputValue;
            } else if (operator) {
                const currentValue = previousInput || 0;
                const newValue = performCalculation(currentValue, inputValue, operator);

                currentInput = String(newValue);
                previousInput = newValue;
                updateDisplay();
            }

            waitingForOperand = true;
            operator = nextOperator;
            history = `${previousInput} ${getOperatorSymbol(operator)}`;
            updateDisplay();
        }

        function getOperatorSymbol(op) {
            switch(op) {
                case '+': return '+';
                case '-': return '-';
                case '*': return '×';
                case '/': return '÷';
                default: return op;
            }
        }

        function calculate() {
            const inputValue = parseFloat(currentInput);

            if (previousInput !== '' && operator && !waitingForOperand) {
                const newValue = performCalculation(previousInput, inputValue, operator);
                
                history = `${previousInput} ${getOperatorSymbol(operator)} ${inputValue} =`;
                currentInput = String(newValue);
                previousInput = '';
                operator = '';
                waitingForOperand = true;
                updateDisplay();
            }
        }

        function performCalculation(firstOperand, secondOperand, operator) {
            switch (operator) {
                case '+':
                    return firstOperand + secondOperand;
                case '-':
                    return firstOperand - secondOperand;
                case '*':
                    return firstOperand * secondOperand;
                case '/':
                    if (secondOperand === 0) {
                        alert('Error: División por cero');
                        return firstOperand;
                    }
                    return firstOperand / secondOperand;
                default:
                    return secondOperand;
            }
        }

        function clearAll() {
            currentInput = '0';
            previousInput = '';
            operator = '';
            waitingForOperand = false;
            history = '';
            updateDisplay();
        }

        function clearEntry() {
            currentInput = '0';
            updateDisplay();
        }

        function backspace() {
            if (currentInput.length > 1) {
                currentInput = currentInput.slice(0, -1);
            } else {
                currentInput = '0';
            }
            updateDisplay();
        }

        function toggleMode(mode) {
            const buttons = document.querySelectorAll('.mode-btn');
            const scientificPanel = document.getElementById('scientificPanel');
            
            buttons.forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
            
            if (mode === 'scientific') {
                scientificPanel.classList.add('active');
                isScientificMode = true;
            } else {
                scientificPanel.classList.remove('active');
                isScientificMode = false;
            }
        }

        function scientificFunction(func) {
            const value = parseFloat(currentInput);
            let result;

            switch(func) {
                case 'sin':
                    result = Math.sin(value * Math.PI / 180);
                    break;
                case 'cos':
                    result = Math.cos(value * Math.PI / 180);
                    break;
                case 'tan':
                    result = Math.tan(value * Math.PI / 180);
                    break;
                case 'log':
                    result = Math.log10(value);
                    break;
                case 'ln':
                    result = Math.log(value);
                    break;
                case 'sqrt':
                    result = Math.sqrt(value);
                    break;
                case 'pow':
                    result = Math.pow(value, 2);
                    break;
                case 'factorial':
                    result = factorial(value);
                    break;
                case 'pi':
                    result = Math.PI;
                    break;
                case 'e':
                    result = Math.E;
                    break;
                case 'abs':
                    result = Math.abs(value);
                    break;
                case '1/x':
                    result = 1 / value;
                    break;
                default:
                    result = value;
            }

            if (isNaN(result) || !isFinite(result)) {
                alert('Error: Operación inválida');
                return;
            }

            history = `${func}(${value}) =`;
            currentInput = String(result);
            waitingForOperand = true;
            updateDisplay();
        }

        function factorial(n) {
            if (n < 0 || n !== Math.floor(n)) {
                return NaN;
            }
            if (n === 0 || n === 1) {
                return 1;
            }
            let result = 1;
            for (let i = 2; i <= n; i++) {
                result *= i;
            }
            return result;
        }

        // Funciones de memoria
        function memoryStore() {
            memory = parseFloat(currentInput);
            showMemoryFeedback('Guardado en memoria');
        }

        function memoryRecall() {
            currentInput = String(memory);
            waitingForOperand = true;
            updateDisplay();
            showMemoryFeedback('Memoria recuperada');
        }

        function memoryAdd() {
            memory += parseFloat(currentInput);
            showMemoryFeedback('Sumado a memoria');
        }

        function memorySubtract() {
            memory -= parseFloat(currentInput);
            showMemoryFeedback('Restado de memoria');
        }

        function memoryClear() {
            memory = 0;
            showMemoryFeedback('Memoria borrada');
        }

        function showMemoryFeedback(message) {
            const originalHistory = history;
            history = message;
            updateDisplay();
            setTimeout(() => {
                history = originalHistory;
                updateDisplay();
            }, 1500);
        }

        // Soporte para teclado
        document.addEventListener('keydown', function(event) {
            const key = event.key;
            
            if (key >= '0' && key <= '9') {
                appendNumber(key);
            } else if (key === '.') {
                appendDecimal();
            } else if (key === '+' || key === '-' || key === '*' || key === '/') {
                appendOperator(key);
            } else if (key === 'Enter' || key === '=') {
                event.preventDefault();
                calculate();
            } else if (key === 'Escape' || key === 'c' || key === 'C') {
                clearAll();
            } else if (key === 'Backspace') {
                event.preventDefault();
                backspace();
            }
        });

        // Inicializar display
        updateDisplay();
    </script>
</body>
</html>