<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Calculadora de Ingeniería</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="calculator">
        <div class="display">
            <input type="text" id="result" readonly>
        </div>
        <div class="buttons">
            <button class="btn clear" onclick="clearDisplay()">C</button>
            <button class="btn operator" onclick="appendToDisplay('/')">/</button>
            <button class="btn operator" onclick="appendToDisplay('*')">*</button>
            <button class="btn operator" onclick="appendToDisplay('-')">-</button>
            <button class="btn" onclick="appendToDisplay('7')">7</button>
            <button class="btn" onclick="appendToDisplay('8')">8</button>
            <button class="btn" onclick="appendToDisplay('9')">9</button>
            <button class="btn operator" onclick="appendToDisplay('+')">+</button>
            <button class="btn" onclick="appendToDisplay('4')">4</button>
            <button class="btn" onclick="appendToDisplay('5')">5</button>
            <button class="btn" onclick="appendToDisplay('6')">6</button>
            <button class="btn" onclick="appendToDisplay('1')">1</button>
            <button class="btn" onclick="appendToDisplay('2')">2</button>
            <button class="btn" onclick="appendToDisplay('3')">3</button>
            <button class="btn equals" onclick="calculateResult()">=</button>
            <button class="btn" onclick="appendToDisplay('0')">0</button>
            <button class="btn" onclick="appendToDisplay('.')">.</button>
            <button class="btn function" onclick="appendToDisplay('Math.PI')">π</button>
            <button class="btn function" onclick="appendToDisplay('Math.E')">e</button>
            <button class="btn function" onclick="appendToDisplay('(')">(</button>
            <button class="btn function" onclick="appendToDisplay(')')">)</button>
            <button class="btn function" onclick="appendToDisplay('Math.sin(')">sin</button>
            <button class="btn function" onclick="appendToDisplay('Math.cos(')">cos</button>
            <button class="btn function" onclick="appendToDisplay('Math.tan(')">tan</button>
            <button class="btn function" onclick="appendToDisplay('Math.log(')">log</button>
            <button class="btn function" onclick="appendToDisplay('Math.sqrt(')">sqrt</button>
            <button class="btn function" onclick="appendToDisplay('Math.pow(')">x^y</button>
            <button class="btn function" onclick="calculateFactorial()">n!</button>
            <button class="btn function" onclick="calculatePercentage()">%</button>
            <button class="btn mode-toggle" onclick="toggleDarkMode()">Modo</button>
        </div>
    </div>
    <script src="script.js"></script>
</body>
</html>