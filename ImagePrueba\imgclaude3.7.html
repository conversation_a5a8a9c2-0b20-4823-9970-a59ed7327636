<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON> de Animalito</title>
    <style>
        body {
            background-color: #c0ffee;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
        }

        .container {
            position: relative;
            width: 300px;
            height: 400px;
        }

        /* Cuerpo principal */
        .body {
            position: absolute;
            width: 180px;
            height: 220px;
            background-color: #e69138;
            border: 5px solid #000;
            border-radius: 50% 50% 50% 50%;
            top: 120px;
            left: 60px;
        }

        /* Barriga */
        .belly {
            position: absolute;
            width: 100px;
            height: 120px;
            background-color: #f6d9a0;
            border: 4px solid #000;
            border-radius: 50%;
            top: 170px;
            left: 100px;
        }

        /* X en la barriga */
        .belly-x:before, .belly-x:after {
            content: '';
            position: absolute;
            background-color: #000;
            width: 20px;
            height: 4px;
            top: 230px;
            left: 140px;
        }
        .belly-x:before {
            transform: rotate(45deg);
        }
        .belly-x:after {
            transform: rotate(-45deg);
        }

        /* Cabeza */
        .head {
            position: absolute;
            width: 160px;
            height: 150px;
            background-color: #e69138;
            border: 5px solid #000;
            border-radius: 50%;
            top: 30px;
            left: 70px;
        }

        /* Hocico */
        .snout {
            position: absolute;
            width: 80px;
            height: 60px;
            background-color: #f6d9a0;
            border: 4px solid #000;
            border-radius: 50%;
            top: 80px;
            left: 110px;
        }

        /* Nariz */
        .nose {
            position: absolute;
            width: 20px;
            height: 10px;
            background-color: #000;
            border-radius: 50%;
            top: 95px;
            left: 140px;
        }

        /* Ojos */
        .eye-left, .eye-right {
            position: absolute;
            width: 12px;
            height: 12px;
            background-color: #000;
            border-radius: 50%;
            top: 70px;
        }
        .eye-left {
            left: 120px;
        }
        .eye-right {
            left: 170px;
        }

        /* Orejas */
        .ear-left, .ear-right {
            position: absolute;
            width: 40px;
            height: 40px;
            background-color: #e69138;
            border: 4px solid #000;
            border-radius: 50%;
            top: 20px;
        }
        .ear-left {
            left: 70px;
        }
        .ear-right {
            left: 190px;
        }

        /* Brazos */
        .arm-left, .arm-right {
            position: absolute;
            width: 40px;
            height: 100px;
            background-color: #e69138;
            border: 4px solid #000;
            border-radius: 30px;
        }
        .arm-left {
            transform: rotate(-30deg);
            top: 150px;
            left: 30px;
        }
        .arm-right {
            transform: rotate(30deg);
            top: 150px;
            left: 230px;
        }

        /* Cola */
        .tail {
            position: absolute;
            width: 80px;
            height: 40px;
            background-color: #e69138;
            border: 4px solid #000;
            border-radius: 20px;
            transform: rotate(30deg);
            top: 200px;
            left: 220px;
        }
        
        /* Rayas de la cola */
        .tail-stripe {
            position: absolute;
            width: 15px;
            height: 30px;
            background-color: #f6d9a0;
            border-radius: 10px;
            transform: rotate(30deg);
            top: 205px;
            left: 250px;
        }

        /* Piernas */
        .leg-left, .leg-right {
            position: absolute;
            width: 30px;
            height: 50px;
            background-color: #e69138;
            border: 4px solid #000;
            border-radius: 15px;
            top: 320px;
        }
        .leg-left {
            left: 100px;
        }
        .leg-right {
            left: 170px;
        }

        /* Brillos */
        .shine {
            position: absolute;
            width: 10px;
            height: 20px;
            background-color: white;
            border-radius: 50%;
            opacity: 0.5;
        }
        .shine-1 {
            top: 60px;
            left: 90px;
            transform: rotate(-30deg);
        }
        .shine-2 {
            top: 150px;
            left: 80px;
            transform: rotate(-20deg);
        }
        .shine-3 {
            top: 180px;
            left: 220px;
            transform: rotate(30deg);
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Cabeza y cara -->
        <div class="head"></div>
        <div class="snout"></div>
        <div class="nose"></div>
        <div class="eye-left"></div>
        <div class="eye-right"></div>
        <div class="ear-left"></div>
        <div class="ear-right"></div>
        
        <!-- Cuerpo -->
        <div class="body"></div>
        <div class="belly"></div>
        <div class="belly-x"></div>
        
        <!-- Extremidades -->
        <div class="arm-left"></div>
        <div class="arm-right"></div>
        <div class="leg-left"></div>
        <div class="leg-right"></div>
        <div class="tail"></div>
        <div class="tail-stripe"></div>
        
        <!-- Efectos de brillo -->
        <div class="shine shine-1"></div>
        <div class="shine shine-2"></div>
        <div class="shine shine-3"></div>
    </div>
</body>
</html>