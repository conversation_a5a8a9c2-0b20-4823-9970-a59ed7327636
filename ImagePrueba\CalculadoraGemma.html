<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Calculadora Avanzada</title>
    <style>
        :root {
            --primary-color: #4a148c;
            --secondary-color: #7b1fa2;
            --accent-color: #e91e63;
            --text-color: #333;
            --bg-color: #f5f5f5;
            --display-bg: #fff;
            --button-bg: #e0e0e0;
            --button-text: #333;
            --button-op-bg: #9c27b0;
            --button-op-text: #fff;
            --button-eq-bg: #e91e63;
            --button-eq-text: #fff;
            --button-fn-bg: #7b1fa2;
            --button-fn-text: #fff;
            --shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            --transition: all 0.3s ease;
        }

        .dark-theme {
            --primary-color: #7b1fa2;
            --secondary-color: #9c27b0;
            --accent-color: #ff4081;
            --text-color: #f5f5f5;
            --bg-color: #121212;
            --display-bg: #1e1e1e;
            --button-bg: #2d2d2d;
            --button-text: #f5f5f5;
            --button-op-bg: #7b1fa2;
            --button-op-text: #fff;
            --button-eq-bg: #ff4081;
            --button-eq-text: #fff;
            --button-fn-bg: #6a1b9a;
            --button-fn-text: #fff;
            --shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            transition: var(--transition);
        }

        body {
            background-color: var(--bg-color);
            color: var(--text-color);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .calculator-container {
            width: 100%;
            max-width: 400px;
            background: linear-gradient(145deg, var(--primary-color), var(--secondary-color));
            border-radius: 20px;
            overflow: hidden;
            box-shadow: var(--shadow);
            position: relative;
        }

        .calculator-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
            background-color: rgba(0, 0, 0, 0.1);
        }

        .calculator-title {
            color: white;
            font-size: 1.2rem;
            font-weight: 600;
        }

        .theme-toggle {
            background: none;
            border: none;
            color: white;
            font-size: 1.2rem;
            cursor: pointer;
            outline: none;
        }

        .display {
            background-color: var(--display-bg);
            padding: 20px;
            text-align: right;
            position: relative;
            box-shadow: inset 0 2px 5px rgba(0, 0, 0, 0.1);
        }

        .history {
            min-height: 20px;
            font-size: 0.9rem;
            color: #777;
            margin-bottom: 5px;
            overflow-x: auto;
            white-space: nowrap;
        }

        .current-input {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--text-color);
            overflow-x: auto;
            white-space: nowrap;
        }

        .buttons {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            gap: 1px;
            background-color: rgba(0, 0, 0, 0.1);
            padding: 1px;
        }

        .btn {
            border: none;
            outline: none;
            padding: 15px 0;
            font-size: 1.2rem;
            background-color: var(--button-bg);
            color: var(--button-text);
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background-color: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            transition: width 0.4s ease, height 0.4s ease;
        }

        .btn:hover::before {
            width: 200px;
            height: 200px;
        }

        .btn:active {
            transform: scale(0.95);
        }

        .btn-operator {
            background-color: var(--button-op-bg);
            color: var(--button-op-text);
        }

        .btn-equals {
            background-color: var(--button-eq-bg);
            color: var(--button-eq-text);
            grid-column: span 2;
        }

        .btn-function {
            background-color: var(--button-fn-bg);
            color: var(--button-fn-text);
        }

        .btn-clear {
            background-color: #f44336;
            color: white;
        }

        .tabs {
            display: flex;
            background-color: rgba(0, 0, 0, 0.1);
        }

        .tab {
            flex: 1;
            padding: 10px;
            text-align: center;
            background-color: rgba(0, 0, 0, 0.05);
            color: white;
            cursor: pointer;
            border: none;
            outline: none;
            transition: var(--transition);
        }

        .tab.active {
            background-color: rgba(255, 255, 255, 0.1);
            font-weight: bold;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: grid;
        }

        .memory-display {
            position: absolute;
            top: 10px;
            left: 10px;
            font-size: 0.8rem;
            color: var(--accent-color);
            font-weight: bold;
        }

        .unit-converter {
            padding: 15px;
            background-color: var(--display-bg);
        }

        .unit-row {
            display: flex;
            margin-bottom: 10px;
            align-items: center;
        }

        .unit-row label {
            width: 100px;
            color: var(--text-color);
        }

        .unit-row select, .unit-row input {
            flex: 1;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: var(--button-bg);
            color: var(--text-color);
        }

        .unit-row select {
            margin-right: 10px;
        }

        .convert-btn {
            width: 100%;
            padding: 10px;
            background-color: var(--button-eq-bg);
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-top: 10px;
        }

        /* Animaciones */
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        @keyframes slideIn {
            from { transform: translateY(20px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }

        .calculator-container {
            animation: slideIn 0.5s ease forwards;
        }

        .btn-equals:hover {
            animation: pulse 1s infinite;
        }

        /* Responsive */
        @media (max-width: 480px) {
            .calculator-container {
                max-width: 100%;
            }

            .btn {
                padding: 12px 0;
                font-size: 1rem;
            }

            .current-input {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="calculator-container">
        <div class="calculator-header">
            <div class="calculator-title">Calculadora Avanzada</div>
            <button class="theme-toggle" id="themeToggle">🌙</button>
        </div>

        <div class="display">
            <div class="memory-display" id="memoryDisplay"></div>
            <div class="history" id="history"></div>
            <div class="current-input" id="currentInput">0</div>
        </div>

        <div class="tabs">
            <button class="tab active" data-tab="standard">Estándar</button>
            <button class="tab" data-tab="scientific">Científica</button>
            <button class="tab" data-tab="converter">Conversor</button>
        </div>

        <div class="tab-content active" id="standard">
            <div class="buttons">
                <button class="btn btn-function" data-action="clear">C</button>
                <button class="btn btn-function" data-action="backspace">⌫</button>
                <button class="btn btn-function" data-action="percent">%</button>
                <button class="btn btn-function" data-action="sqrt">√</button>
                <button class="btn btn-operator" data-action="divide">÷</button>

                <button class="btn" data-value="7">7</button>
                <button class="btn" data-value="8">8</button>
                <button class="btn" data-value="9">9</button>
                <button class="btn btn-function" data-action="square">x²</button>
                <button class="btn btn-operator" data-action="multiply">×</button>

                <button class="btn" data-value="4">4</button>
                <button class="btn" data-value="5">5</button>
                <button class="btn" data-value="6">6</button>
                <button class="btn btn-function" data-action="reciprocal">1/x</button>
                <button class="btn btn-operator" data-action="subtract">-</button>

                <button class="btn" data-value="1">1</button>
                <button class="btn" data-value="2">2</button>
                <button class="btn" data-value="3">3</button>
                <button class="btn btn-function" data-memory="mplus">M+</button>
                <button class="btn btn-operator" data-action="add">+</button>

                <button class="btn" data-value="0">0</button>
                <button class="btn" data-value=".">.</button>
                <button class="btn btn-function" data-memory="mr">MR</button>
                <button class="btn btn-equals" data-action="calculate">=</button>
            </div>
        </div>

        <div class="tab-content" id="scientific">
            <div class="buttons">
                <button class="btn btn-function" data-action="clear">C</button>
                <button class="btn btn-function" data-action="backspace">⌫</button>
                <button class="btn btn-function" data-action="percent">%</button>
                <button class="btn btn-function" data-action="sqrt">√</button>
                <button class="btn btn-operator" data-action="divide">÷</button>

                <button class="btn btn-function" data-action="sin">sin</button>
                <button class="btn" data-value="7">7</button>
                <button class="btn" data-value="8">8</button>
                <button class="btn" data-value="9">9</button>
                <button class="btn btn-operator" data-action="multiply">×</button>

                <button class="btn btn-function" data-action="cos">cos</button>
                <button class="btn" data-value="4">4</button>
                <button class="btn" data-value="5">5</button>
                <button class="btn" data-value="6">6</button>
                <button class="btn btn-operator" data-action="subtract">-</button>

                <button class="btn btn-function" data-action="tan">tan</button>
                <button class="btn" data-value="1">1</button>
                <button class="btn" data-value="2">2</button>
                <button class="btn" data-value="3">3</button>
                <button class="btn btn-operator" data-action="add">+</button>

                <button class="btn btn-function" data-action="log">log</button>
                <button class="btn" data-value="0">0</button>
                <button class="btn" data-value=".">.</button>
                <button class="btn btn-function" data-action="power">x^y</button>
                <button class="btn btn-equals" data-action="calculate">=</button>
            </div>
        </div>

        <div class="tab-content" id="converter">
            <div class="unit-converter">
                <div class="unit-row">
                    <label>Tipo:</label>
                    <select id="conversionType">
                        <option value="length">Longitud</option>
                        <option value="weight">Peso</option>
                        <option value="temperature">Temperatura</option>
                        <option value="area">Área</option>
                        <option value="volume">Volumen</option>
                    </select>
                </div>
                <div class="unit-row">
                    <label>De:</label>
                    <select id="fromUnit"></select>
                    <input type="number" id="fromValue" value="1">
                </div>
                <div class="unit-row">
                    <label>A:</label>
                    <select id="toUnit"></select>
                    <input type="number" id="toValue" readonly>
                </div>
                <button class="convert-btn" id="convertBtn">Convertir</button>
            </div>
        </div>
    </div>

    <script>
        // Variables globales
        let currentInput = '0';
        let previousInput = '';
        let operation = null;
        let shouldResetInput = false;
        let memory = 0;
        let isInRadians = true;

        // Elementos DOM
        const currentInputEl = document.getElementById('currentInput');
        const historyEl = document.getElementById('history');
        const memoryDisplayEl = document.getElementById('memoryDisplay');
        const themeToggleBtn = document.getElementById('themeToggle');
        const tabs = document.querySelectorAll('.tab');
        const tabContents = document.querySelectorAll('.tab-content');

        // Conversor de unidades
        const conversionTypeEl = document.getElementById('conversionType');
        const fromUnitEl = document.getElementById('fromUnit');
        const toUnitEl = document.getElementById('toUnit');
        const fromValueEl = document.getElementById('fromValue');
        const toValueEl = document.getElementById('toValue');
        const convertBtn = document.getElementById('convertBtn');

        // Unidades de conversión
        const units = {
            length: {
                metro: 1,
                kilómetro: 0.001,
                centímetro: 100,
                milímetro: 1000,
                pulgada: 39.3701,
                pie: 3.28084,
                yarda: 1.09361,
                milla: 0.000621371
            },
            weight: {
                kilogramo: 1,
                gramo: 1000,
                miligramo: 1000000,
                tonelada: 0.001,
                libra: 2.20462,
                onza: 35.274
            },
            temperature: {
                celsius: 'C',
                fahrenheit: 'F',
                kelvin: 'K'
            },
            area: {
                'm²': 1,
                'km²': 0.000001,
                'cm²': 10000,
                'hectárea': 0.0001,
                'acre': 0.000247105
            },
            volume: {
                'litro': 1,
                'mililitro': 1000,
                'm³': 0.001,
                'galón': 0.264172,
                'pinta': 2.11338
            }
        };

        // Inicializar conversor
        function initializeConverter() {
            updateUnitOptions();
            conversionTypeEl.addEventListener('change', updateUnitOptions);
            convertBtn.addEventListener('click', performConversion);
            fromValueEl.addEventListener('input', performConversion);
            fromUnitEl.addEventListener('change', performConversion);
            toUnitEl.addEventListener('change', performConversion);
        }

        // Actualizar opciones de unidades
        function updateUnitOptions() {
            const type = conversionTypeEl.value;
            fromUnitEl.innerHTML = '';
            toUnitEl.innerHTML = '';
            
            for (const unit in units[type]) {
                const fromOption = document.createElement('option');
                fromOption.value = unit;
                fromOption.textContent = unit;
                fromUnitEl.appendChild(fromOption);
                
                const toOption = document.createElement('option');
                toOption.value = unit;
                toOption.textContent = unit;
                toUnitEl.appendChild(toOption);
            }
            
            // Seleccionar una opción diferente por defecto para 'to'
            if (toUnitEl.options.length > 1) {
                toUnitEl.selectedIndex = 1;
            }
            
            performConversion();
        }

        // Realizar conversión
        function performConversion() {
            const type = conversionTypeEl.value;
            const fromUnit = fromUnitEl.value;
            const toUnit = toUnitEl.value;
            const fromValue = parseFloat(fromValueEl.value);
            
            if (isNaN(fromValue)) {
                toValueEl.value = '';
                return;
            }
            
            let result;
            
            if (type === 'temperature') {
                // Conversión especial para temperatura
                if (fromUnit === toUnit) {
                    result = fromValue;
                } else if (fromUnit === 'celsius' && toUnit === 'fahrenheit') {
                    result = (fromValue * 9/5) + 32;
                } else if (fromUnit === 'celsius' && toUnit === 'kelvin') {
                    result = fromValue + 273.15;
                } else if (fromUnit === 'fahrenheit' && toUnit === 'celsius') {
                    result = (fromValue - 32) * 5/9;
                } else if (fromUnit === 'fahrenheit' && toUnit === 'kelvin') {
                    result = (fromValue - 32) * 5/9 + 273.15;
                } else if (fromUnit === 'kelvin' && toUnit === 'celsius') {
                    result = fromValue - 273.15;
                } else if (fromUnit === 'kelvin' && toUnit === 'fahrenheit') {
                    result = (fromValue - 273.15) * 9/5 + 32;
                }
            } else {
                // Conversión estándar para otras unidades
                const baseValue = fromValue / units[type][fromUnit];
                result = baseValue * units[type][toUnit];
            }
            
            toValueEl.value = result.toFixed(6);
        }

        // Cambiar tema
        themeToggleBtn.addEventListener('click', () => {
            document.body.classList.toggle('dark-theme');
            themeToggleBtn.textContent = document.body.classList.contains('dark-theme') ? '☀️' : '🌙';
        });

        // Cambiar pestañas
        tabs.forEach(tab => {
            tab.addEventListener('click', () => {
                tabs.forEach(t => t.classList.remove('active'));
                tabContents.forEach(tc => tc.classList.remove('active'));
                
                tab.classList.add('active');
                document.getElementById(tab.dataset.tab).classList.add('active');
            });
        });

        // Actualizar pantalla
        function updateDisplay() {
            currentInputEl.textContent = currentInput;
        }

        // Actualizar historial
        function updateHistory(text) {
            historyEl.textContent = text;
        }

        // Actualizar memoria
        function updateMemoryDisplay() {
            memoryDisplayEl.textContent = memory !== 0 ? `M: ${memory}` : '';
        }

        // Añadir dígito
        function appendDigit(digit) {
            if (shouldResetInput) {
                currentInput = digit;
                shouldResetInput = false;
            } else {
                currentInput = currentInput === '0' ? digit : currentInput + digit;
            }
            updateDisplay();
        }

        // Añadir punto decimal
        function appendDecimal() {
            if (shouldResetInput) {
                currentInput = '0.';
                shouldResetInput = false;
            } else if (!currentInput.includes('.')) {
                currentInput += '.';
            }
            updateDisplay();
        }

        // Operaciones básicas
        function handleOperation(op) {
            const inputValue = parseFloat(currentInput);
            
            if (previousInput === '') {
                previousInput = currentInput;
                operation = op;
                shouldResetInput = true;
                updateHistory(`${currentInput} ${getOperationSymbol(op)}`);
            } else {
                // Realizar la operación pendiente
                const result = calculate();
                previousInput = result.toString();
                currentInput = result.toString();
                operation = op;
                shouldResetInput = true;
                updateHistory(`${result} ${getOperationSymbol(op)}`);
            }
        }

        // Obtener símbolo de operación
        function getOperationSymbol(op) {
            switch(op) {
                case 'add': return '+';
                case 'subtract': return '-';
                case 'multiply': return '×';
                case 'divide': return '÷';
                case 'power': return '^';
                default: return '';
            }
        }

        // Calcular resultado
        function calculate() {
            if (operation === null || previousInput === '') {
                return parseFloat(currentInput);
            }
            
            const prev = parseFloat(previousInput);
            const current = parseFloat(currentInput);
            let result;
            
            switch(operation) {
                case 'add':
                    result = prev + current;
                    break;
                case 'subtract':
                    result = prev - current;
                    break;
                case 'multiply':
                    result = prev * current;
                    break;
                case 'divide':
                    if (current === 0) {
                        alert('Error: División por cero');
                        clearAll();
                        return 0;
                    }
                    result = prev / current;
                    break;
                case 'power':
                    result = Math.pow(prev, current);
                    break;
                default:
                    return current;
            }
            
            updateHistory(`${previousInput} ${getOperationSymbol(operation)} ${currentInput} =`);
            operation = null;
            previousInput = '';
            return result;
        }

        // Funciones científicas
        function handleFunction(func) {
            const inputValue = parseFloat(currentInput);
            let result;
            
            switch(func) {
                case 'sqrt':
                    if (inputValue < 0) {
                        alert('Error: No se puede calcular la raíz cuadrada de un número negativo');
                        return;
                    }
                    result = Math.sqrt(inputValue);
                    updateHistory(`√(${inputValue}) =`);
                    break;
                case 'square':
                    result = inputValue * inputValue;
                    updateHistory(`(${inputValue})² =`);
                    break;
                case 'reciprocal':
                    if (inputValue === 0) {
                        alert('Error: No se puede dividir por cero');
                        return;
                    }
                    result = 1 / inputValue;
                    updateHistory(`1/(${inputValue}) =`);
                    break;
                case 'percent':
                    result = inputValue / 100;
                    updateHistory(`${inputValue}% =`);
                    break;
                case 'sin':
                    result = isInRadians ? Math.sin(inputValue) : Math.sin(inputValue * Math.PI / 180);
                    updateHistory(`sin(${inputValue}) =`);
                    break;
                case 'cos':
                    result = isInRadians ? Math.cos(inputValue) : Math.cos(inputValue * Math.PI / 180);
                    updateHistory(`cos(${inputValue}) =`);
                    break;
                case 'tan':
                    result = isInRadians ? Math.tan(inputValue) : Math.tan(inputValue * Math.PI / 180);
                    updateHistory(`tan(${inputValue}) =`);
                    break;
                case 'log':
                    if (inputValue <= 0) {
                        alert('Error: No se puede calcular el logaritmo de un número no positivo');
                        return;
                    }
                    result = Math.log10(inputValue);
                    updateHistory(`log(${inputValue}) =`);
                    break;
            }
            
            currentInput = result.toString();
            shouldResetInput = true;
            updateDisplay();
        }

        // Funciones de memoria
        function handleMemory(action) {
            const inputValue = parseFloat(currentInput);
            
            switch(action) {
                case 'mplus':
                    memory += inputValue;
                    break;
                case 'mr':
                    currentInput = memory.toString();
                    updateDisplay();
                    break;
            }
            
            updateMemoryDisplay();
        }

        // Limpiar todo
        function clearAll() {
            currentInput = '0';
            previousInput = '';
            operation = null;
            shouldResetInput = false;
            updateDisplay();
            updateHistory('');
        }

        // Limpiar entrada
        function clearInput() {
            currentInput = '0';
            updateDisplay();
        }

        // Borrar último dígito
        function backspace() {
            if (currentInput.length === 1) {
                currentInput = '0';
            } else {
                currentInput = currentInput.slice(0, -1);
            }
            updateDisplay();
        }

        // Establecer operación
        function setOperation(op) {
            const inputNum = parseFloat(currentInput);
            if (previousInput === '') {
                previousInput = currentInput;
            } else if (operation) {
                previousInput = calculate().toString();
            }
            operation = op;
            updateHistory(`${previousInput} ${getOperationSymbol(operation)}`);
            shouldResetInput = true;
        }

        // Obtener símbolo de operación
        function getOperationSymbol(op) {
            switch(op) {
                case 'add': return '+';
                case 'subtract': return '-';
                case 'multiply': return '×';
                case 'divide': return '÷';
                case 'power': return '^';
                default: return '';
            }
        }

        // Calcular resultado
        function calculate() {
            const prev = parseFloat(previousInput);
            const current = parseFloat(currentInput);
            let result = 0;
            
            switch(operation) {
                case 'add':
                    result = prev + current;
                    break;
                case 'subtract':
                    result = prev - current;
                    break;
                case 'multiply':
                    result = prev * current;
                    break;
                case 'divide':
                    result = prev / current;
                    break;
                case 'power':
                    result = Math.pow(prev, current);
                    break;
                default:
                    return current;
            }
            
            updateHistory(`${previousInput} ${getOperationSymbol(operation)} ${currentInput} =`);
            previousInput = '';
            operation = null;
            shouldResetInput = true;
            return result;
        }

        // Funciones matemáticas
        function percent() {
            currentInput = (parseFloat(currentInput) / 100).toString();
            updateDisplay();
        }

        function sqrt() {
            currentInput = Math.sqrt(parseFloat(currentInput)).toString();
            updateDisplay();
        }

        function square() {
            currentInput = (parseFloat(currentInput) ** 2).toString();
            updateDisplay();
        }

        function reciprocal() {
            currentInput = (1 / parseFloat(currentInput)).toString();
            updateDisplay();
        }

        function sin() {
            const value = parseFloat(currentInput);
            currentInput = (isInRadians ? Math.sin(value) : Math.sin(value * Math.PI / 180)).toString();
            updateDisplay();
        }

        function cos() {
            const value = parseFloat(currentInput);
            currentInput = (isInRadians ? Math.cos(value) : Math.cos(value * Math.PI / 180)).toString();
            updateDisplay();
        }

        function tan() {
            const value = parseFloat(currentInput);
            currentInput = (isInRadians ? Math.tan(value) : Math.tan(value * Math.PI / 180)).toString();
            updateDisplay();
        }

        function log() {
            currentInput = Math.log10(parseFloat(currentInput)).toString();
            updateDisplay();
        }

        // Funciones de memoria
        function memoryAdd() {
            memory += parseFloat(currentInput);
            shouldResetInput = true;
            updateMemoryDisplay();
        }

        function memoryRecall() {
            currentInput = memory.toString();
            updateDisplay();
        }

        // Event listeners para botones
        document.querySelectorAll('.btn').forEach(button => {
            button.addEventListener('click', () => {
                // Efecto de onda al hacer clic
                const circle = document.createElement('div');
                circle.style.position = 'absolute';
                circle.style.borderRadius = '50%';
                circle.style.backgroundColor = 'rgba(255, 255, 255, 0.3)';
                circle.style.width = '100px';
                circle.style.height = '100px';
                circle.style.top = '50%';
                circle.style.left = '50%';
                circle.style.transform = 'translate(-50%, -50%) scale(0)';
                circle.style.animation = 'ripple 0.6s linear';
                button.appendChild(circle);
                
                setTimeout(() => {
                    circle.remove();
                }, 700);
                
                // Procesar acción del botón
                if (button.dataset.value) {
                    appendDigit(button.dataset.value);
                } else if (button.dataset.action) {
                    switch(button.dataset.action) {
                        case 'clear':
                            clearInput();
                            updateHistory('');
                            break;
                        case 'backspace':
                            backspace();
                            break;
                        case 'calculate':
                            currentInput = calculate().toString();
                            updateDisplay();
                            break;
                        case 'add':
                        case 'subtract':
                        case 'multiply':
                        case 'divide':
                        case 'power':
                            setOperation(button.dataset.action);
                            break;
                        case 'percent':
                            percent();
                            break;
                        case 'sqrt':
                            sqrt();
                            break;
                        case 'square':
                            square();
                            break;
                        case 'reciprocal':
                            reciprocal();
                            break;
                        case 'sin':
                            sin();
                            break;
                        case 'cos':
                            cos();
                            break;
                        case 'tan':
                            tan();
                            break;
                        case 'log':
                            log();
                            break;
                    }
                } else if (button.dataset.memory) {
                    switch(button.dataset.memory) {
                        case 'mplus':
                            memoryAdd();
                            break;
                        case 'mr':
                            memoryRecall();
                            break;
                    }
                }
            });
        });

        // Añadir keydown event para teclado
        document.addEventListener('keydown', (e) => {
            if (e.key >= '0' && e.key <= '9') {
                appendDigit(e.key);
            } else if (e.key === '.') {
                appendDecimal();
            } else if (e.key === '+') {
                setOperation('add');
            } else if (e.key === '-') {
                setOperation('subtract');
            } else if (e.key === '*') {
                setOperation('multiply');
            } else if (e.key === '/') {
                setOperation('divide');
            } else if (e.key === 'Enter' || e.key === '=') {
                currentInput = calculate().toString();
                updateDisplay();
            } else if (e.key === 'Escape') {
                clearInput();
                updateHistory('');
            } else if (e.key === 'Backspace') {
                backspace();
            }
        });

        // Inicializar
        updateDisplay();
        updateMemoryDisplay();
        initializeConverter();

        // Añadir animación de ripple
        const style = document.createElement('style');
        style.textContent = `
            @keyframes ripple {
                to {
                    transform: translate(-50%, -50%) scale(3);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>