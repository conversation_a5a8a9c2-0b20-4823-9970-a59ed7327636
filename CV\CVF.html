<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON><PERSON> - CV</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            display: grid;
            grid-template-columns: 350px 1fr;
            gap: 30px;
            min-height: 100vh;
            animation: fadeIn 1s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .sidebar {
            background: white;
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            padding: 30px;
            height: fit-content;
            position: sticky;
            top: 20px;
            animation: slideInLeft 0.8s ease-out;
        }

        @keyframes slideInLeft {
            from { transform: translateX(-100px); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }

        .main-content {
            background: white;
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            padding: 40px;
            animation: slideInRight 0.8s ease-out;
        }

        @keyframes slideInRight {
            from { transform: translateX(100px); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }

        .profile-section {
            text-align: center;
            margin-bottom: 30px;
        }

        .photo-container {
            width: 150px;
            height: 150px;
            margin: 0 auto 20px;
            border-radius: 50%;
            overflow: hidden;
            border: 5px solid #667eea;
            position: relative;
            cursor: pointer;
            transition: all 0.3s ease;
            background: linear-gradient(135deg, #667eea, #764ba2);
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .photo-container:hover {
            transform: scale(1.05);
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
        }

        .photo-placeholder {
            color: white;
            font-size: 48px;
            opacity: 0.7;
        }

        .profile-photo {
            width: 100%;
            height: 100%;
            object-fit: cover;
            display: none;
        }

        .file-input {
            display: none;
        }

        .upload-btn {
            background: #667eea;
            color: white;
            padding: 8px 16px;
            border: none;
            border-radius: 20px;
            cursor: pointer;
            font-size: 12px;
            margin-top: 10px;
            transition: all 0.3s ease;
        }

        .upload-btn:hover {
            background: #5a6fd8;
            transform: translateY(-2px);
        }

        .name {
            font-size: 24px;
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }

        .title {
            color: #667eea;
            font-size: 16px;
            margin-bottom: 20px;
        }

        .contact-info {
            background: #f8f9ff;
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 25px;
        }

        .contact-item {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
            padding: 5px 0;
            transition: all 0.3s ease;
        }

        .contact-item:hover {
            transform: translateX(5px);
            color: #667eea;
        }

        .contact-item i {
            width: 20px;
            color: #667eea;
            margin-right: 10px;
        }

        .contact-item a {
            color: inherit;
            text-decoration: none;
        }

        .section {
            margin-bottom: 30px;
        }

        .section-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 15px;
            padding-bottom: 8px;
            border-bottom: 2px solid #667eea;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 0;
            height: 2px;
            background: #764ba2;
            transition: width 0.3s ease;
        }

        .section:hover .section-title::after {
            width: 100%;
        }

        .skill-item, .language-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
            padding: 8px 0;
        }

        .skill-bar {
            width: 100px;
            height: 6px;
            background: #e1e5fe;
            border-radius: 3px;
            overflow: hidden;
        }

        .skill-progress {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 3px;
            animation: growBar 1.5s ease-out;
        }

        @keyframes growBar {
            from { width: 0; }
        }

        .main-section {
            margin-bottom: 40px;
            opacity: 0;
            animation: fadeInUp 0.8s ease-out forwards;
        }

        .main-section:nth-child(1) { animation-delay: 0.2s; }
        .main-section:nth-child(2) { animation-delay: 0.4s; }
        .main-section:nth-child(3) { animation-delay: 0.6s; }
        .main-section:nth-child(4) { animation-delay: 0.8s; }

        @keyframes fadeInUp {
            from { 
                opacity: 0; 
                transform: translateY(30px); 
            }
            to { 
                opacity: 1; 
                transform: translateY(0); 
            }
        }

        .main-section-title {
            font-size: 28px;
            color: #333;
            margin-bottom: 20px;
            position: relative;
            display: inline-block;
        }

        .main-section-title::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 0;
            width: 100%;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .experience-item, .education-item, .project-item {
            background: #f8f9ff;
            padding: 25px;
            border-radius: 15px;
            margin-bottom: 20px;
            border-left: 4px solid #667eea;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .experience-item:hover, .education-item:hover, .project-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.15);
        }

        .item-title {
            font-size: 20px;
            font-weight: bold;
            color: #333;
            margin-bottom: 8px;
        }

        .item-subtitle {
            color: #667eea;
            font-weight: 600;
            margin-bottom: 5px;
        }

        .item-date {
            color: #888;
            font-size: 14px;
            margin-bottom: 10px;
        }

        .github-link {
            display: inline-block;
            color: #667eea;
            text-decoration: none;
            font-weight: 600;
            margin-top: 10px;
            transition: all 0.3s ease;
        }

        .github-link:hover {
            color: #764ba2;
            transform: translateX(5px);
        }

        .floating-elements {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }

        .floating-element {
            position: absolute;
            background: rgba(255,255,255,0.1);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }

        .floating-element:nth-child(1) {
            width: 80px;
            height: 80px;
            top: 20%;
            left: 10%;
            animation-delay: 0s;
        }

        .floating-element:nth-child(2) {
            width: 60px;
            height: 60px;
            top: 60%;
            right: 10%;
            animation-delay: 2s;
        }

        .floating-element:nth-child(3) {
            width: 40px;
            height: 40px;
            bottom: 20%;
            left: 15%;
            animation-delay: 4s;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .container {
                grid-template-columns: 1fr;
                gap: 20px;
                padding: 10px;
            }

            .sidebar {
                position: relative;
                top: 0;
            }

            .main-content {
                padding: 25px;
            }

            .photo-container {
                width: 120px;
                height: 120px;
            }

            .name {
                font-size: 20px;
            }

            .main-section-title {
                font-size: 24px;
            }

            .contact-item {
                font-size: 14px;
            }
        }

        @media (max-width: 480px) {
            .main-content {
                padding: 20px;
            }

            .experience-item, .education-item, .project-item {
                padding: 20px;
            }

            .item-title {
                font-size: 18px;
            }
        }
    </style>
</head>
<body>
    <div class="floating-elements">
        <div class="floating-element"></div>
        <div class="floating-element"></div>
        <div class="floating-element"></div>
    </div>

    <div class="container">
        <!-- Sidebar -->
        <div class="sidebar">
            <div class="profile-section">
                <div class="photo-container" onclick="document.getElementById('photoInput').click()">
                    <i class="fas fa-user photo-placeholder"></i>
                    <img id="profilePhoto" class="profile-photo" alt="Foto de perfil">
                </div>
                <input type="file" id="photoInput" class="file-input" accept="image/*">
                
                <h1 class="name">Fabrizio Mendoza Huaranga</h1>
                <p class="title">Estudiante de Ingeniería de Sistemas</p>
            </div>

            <div class="contact-info">
                <div class="contact-item">
                    <i class="fas fa-envelope"></i>
                    <a href="mailto:<EMAIL>"><EMAIL></a>
                </div>
                <div class="contact-item">
                    <i class="fas fa-phone"></i>
                    <span>+51 966 388 640</span>
                </div>
                <div class="contact-item">
                    <i class="fas fa-id-card"></i>
                    <span>72286874</span>
                </div>
                <div class="contact-item">
                    <i class="fas fa-map-marker-alt"></i>
                    <span>Ventanilla - Callao</span>
                </div>
                <div class="contact-item">
                    <i class="fab fa-linkedin"></i>
                    <a href="https://www.linkedin.com/in/fabmenhua" target="_blank">LinkedIn</a>
                </div>
            </div>

            <div class="section">
                <h3 class="section-title">Habilidades Técnicas</h3>
                <div class="skill-item">
                    <span>Python</span>
                    <div class="skill-bar">
                        <div class="skill-progress" style="width: 30%"></div>
                    </div>
                </div>
                <div class="skill-item">
                    <span>Java</span>
                    <div class="skill-bar">
                        <div class="skill-progress" style="width: 40%"></div>
                    </div>
                </div>
                <div class="skill-item">
                    <span>SQL</span>
                    <div class="skill-bar">
                        <div class="skill-progress" style="width: 40%"></div>
                    </div>
                </div>
                <div class="skill-item">
                    <span>HTML/CSS</span>
                    <div class="skill-bar">
                        <div class="skill-progress" style="width: 35%"></div>
                    </div>
                </div>
                <div class="skill-item">
                    <span>Git</span>
                    <div class="skill-bar">
                        <div class="skill-progress" style="width: 30%"></div>
                    </div>
                </div>
            </div>

            <div class="section">
                <h3 class="section-title">Idiomas</h3>
                <div class="language-item">
                    <span>Español</span>
                    <span style="color: #667eea; font-weight: 600;">Nativo</span>
                </div>
                <div class="language-item">
                    <span>Inglés</span>
                    <span style="color: #667eea; font-weight: 600;">Básico</span>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <div class="main-section">
                <h2 class="main-section-title">Perfil Profesional</h2>
                <p>Estudiante de Ingeniería de Sistemas en el séptimo ciclo en la Universidad César Vallejo, con conocimientos en programación (Python, Java), bases de datos (SQL) y desarrollo web (HTML, CSS). Apasionado por crear soluciones tecnológicas innovadoras, busco oportunidades para aplicar mis habilidades en proyectos de desarrollo de software y crecer profesionalmente en el área de tecnología.</p>
            </div>

            <div class="main-section">
                <h2 class="main-section-title">Formación Académica</h2>
                <div class="education-item">
                    <h3 class="item-title">Ingeniería de Sistemas</h3>
                    <p class="item-subtitle">Universidad César Vallejo (UCV)</p>
                    <p class="item-date">Séptimo ciclo - 2025 (En curso)</p>
                    <p>Ventanilla, Callao</p>
                </div>
            </div>

            <div class="main-section">
                <h2 class="main-section-title">Formación Complementaria</h2>
                <div class="education-item">
                    <h3 class="item-title">Taller de Ciberseguridad Básica</h3>
                    <p class="item-subtitle">Universidad César Vallejo</p>
                    <p class="item-date">2025</p>
                    <p>Principios para proteger sistemas y datos en entornos digitales.</p>
                </div>
                <div class="education-item">
                    <h3 class="item-title">Taller de Introducción a la Programación en Python</h3>
                    <p class="item-subtitle">Universidad César Vallejo</p>
                    <p class="item-date">2024</p>
                    <p>Fundamentos de programación para análisis de datos.</p>
                </div>
                <div class="education-item">
                    <h3 class="item-title">Curso de Machine Learning Básico</h3>
                    <p class="item-date">2024</p>
                    <p>Conceptos iniciales de algoritmos de aprendizaje automático.</p>
                </div>
                <div class="education-item">
                    <h3 class="item-title">Curso de Bases de Datos Básico</h3>
                    <p class="item-date">2024</p>
                    <p>Introducción a SQL y gestión de bases de datos.</p>
                </div>
                <div class="education-item">
                    <h3 class="item-title">Curso de Hardware y Software</h3>
                    <p class="item-date">2024</p>
                    <p>Fundamentos de ensamblaje y sistemas operativos.</p>
                </div>
            </div>

            <div class="main-section">
                <h2 class="main-section-title">Proyectos</h2>
                <div class="project-item">
                    <h3 class="item-title">Sistema CRUD para Tienda de Abarrotes</h3>
                    <p class="item-subtitle">Proyecto Personal</p>
                    <p class="item-date">2024</p>
                    <p>Creación de una aplicación web para gestionar inventario y ventas, utilizando tecnologías web modernas con conexión a base de datos.</p>
                    <a href="https://github.com/BFree-783/CRUDAbarrotes.git" target="_blank" class="github-link">
                        <i class="fab fa-github"></i> Ver en GitHub
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Funcionalidad para subir foto
        document.getElementById('photoInput').addEventListener('change', function(event) {
            const file = event.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const img = document.getElementById('profilePhoto');
                    const placeholder = document.querySelector('.photo-placeholder');
                    
                    img.src = e.target.result;
                    img.style.display = 'block';
                    placeholder.style.display = 'none';
                };
                reader.readAsDataURL(file);
            }
        });

        // Animación de las barras de habilidades al hacer scroll
        function animateSkillBars() {
            const skillBars = document.querySelectorAll('.skill-progress');
            skillBars.forEach(bar => {
                const rect = bar.getBoundingClientRect();
                if (rect.top < window.innerHeight && rect.bottom > 0) {
                    bar.style.animation = 'none';
                    bar.offsetHeight; // Trigger reflow
                    bar.style.animation = 'growBar 1.5s ease-out';
                }
            });
        }

        // Efecto de parallax suave en elementos flotantes
        function updateFloatingElements() {
            const elements = document.querySelectorAll('.floating-element');
            const scrollTop = window.pageYOffset;
            
            elements.forEach((element, index) => {
                const speed = 0.5 + (index * 0.2);
                const yPos = -(scrollTop * speed);
                element.style.transform = `translate3d(0, ${yPos}px, 0)`;
            });
        }

        // Event listeners
        window.addEventListener('scroll', () => {
            animateSkillBars();
            updateFloatingElements();
        });

        window.addEventListener('load', animateSkillBars);

        // Efecto de escritura en el nombre
        function typeWriter() {
            const nameElement = document.querySelector('.name');
            const text = nameElement.textContent;
            nameElement.textContent = '';
            
            let i = 0;
            const timer = setInterval(() => {
                if (i < text.length) {
                    nameElement.textContent += text.charAt(i);
                    i++;
                } else {
                    clearInterval(timer);
                }
            }, 100);
        }

        // Ejecutar efecto de escritura después de la carga
        setTimeout(typeWriter, 1000);

        // Efecto hover en elementos de contacto
        document.querySelectorAll('.contact-item').forEach(item => {
            item.addEventListener('mouseenter', function() {
                this.style.backgroundColor = 'rgba(102, 126, 234, 0.1)';
                this.style.borderRadius = '8px';
                this.style.padding = '8px 12px';
            });
            
            item.addEventListener('mouseleave', function() {
                this.style.backgroundColor = 'transparent';
                this.style.padding = '5px 0';
            });
        });

        // Smooth scroll para enlaces internos (si los hay)
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    </script>
</body>
</html>