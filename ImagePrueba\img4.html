<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <title>Cara de Gato <PERSON>ioso</title>
    <style>
        body {
            background: #f0f0f0;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
        }
        .cat-face {
            position: relative;
            width: 300px;
            height: 300px;
            background: #FFD966;
            border-radius: 50%;
            border: 4px solid #333;
        }
        .ear {
            position: absolute;
            width: 90px;
            height: 90px;
            background: #FFD966;
            border: 4px solid #333;
            top: -60px;
            border-radius: 60% 60% 0 0;
            z-index: 1;
        }
        .ear.left {
            left: -30px;
            transform: rotate(-20deg);
        }
        .ear.right {
            right: -30px;
            transform: rotate(20deg);
        }
        .inner-ear {
            position: absolute;
            width: 50px;
            height: 50px;
            background: #F9B7B7;
            top: 15px;
            left: 20px;
            border-radius: 60% 60% 0 0;
        }
        .eye {
            position: absolute;
            width: 50px;
            height: 60px;
            background: #fff;
            border: 3px solid #333;
            border-radius: 50%;
            top: 90px;
        }
        .eye.left {
            left: 55px;
        }
        .eye.right {
            right: 55px;
        }
        .pupil {
            position: absolute;
            width: 18px;
            height: 28px;
            background: #333;
            border-radius: 50%;
            top: 18px;
            left: 16px;
        }
        .nose {
            position: absolute;
            top: 170px;
            left: 125px;
            width: 50px;
            height: 30px;
            background: #F9B7B7;
            border-radius: 50% 50% 60% 60%;
            border: 3px solid #333;
        }
        .mouth {
            position: absolute;
            top: 200px;
            left: 110px;
            width: 80px;
            height: 50px;
            border-bottom: 4px solid #333;
            border-radius: 0 0 80px 80px;
        }
        .whiskers {
            position: absolute;
            width: 120px;
            height: 20px;
            top: 190px;
        }
        .whiskers.left {
            left: -70px;
            transform: rotate(-10deg);
        }
        .whiskers.right {
            right: -70px;
            transform: rotate(10deg);
        }
        .whisker {
            position: absolute;
            width: 60px;
            height: 3px;
            background: #333;
            border-radius: 2px;
        }
        .whisker1 { top: 0; left: 0; }
        .whisker2 { top: 8px; left: 10px; }
        .whisker3 { top: 16px; left: 20px; }
        .cheek {
            position: absolute;
            width: 40px;
            height: 20px;
            background: #F9B7B7;
            border-radius: 50%;
            top: 210px;
        }
        .cheek.left { left: 40px; }
        .cheek.right { right: 40px; }
        .tongue {
            position: absolute;
            top: 235px; /* Bajamos la lengua */
            left: 130px; /* Centramos mejor la lengua */
            width: 40px;
            height: 40px;
            background: #ff6b81;
            border-radius: 0 0 40px 40px;
            border-bottom: 4px solid #b03a48;
            z-index: 2;
            animation: tongue-move 1s infinite alternate;
        }
        @keyframes tongue-move {
            0% { transform: translateY(0) scaleY(1); }
            100% { transform: translateY(8px) scaleY(1.1); }
        }
    </style>
</head>
<body>
    <div class="cat-face">
        <div class="ear left">
            <div class="inner-ear"></div>
        </div>
        <div class="ear right">
            <div class="inner-ear"></div>
        </div>
        <div class="eye left">
            <div class="pupil"></div>
        </div>
        <div class="eye right">
            <div class="pupil"></div>
        </div>
        <div class="nose"></div>
        <div class="tongue"></div>
        <div class="mouth"></div>
        <div class="whiskers left">
            <div class="whisker whisker1"></div>
            <div class="whisker whisker2"></div>
            <div class="whisker whisker3"></div>
        </div>
        <div class="whiskers right">
            <div class="whisker whisker1"></div>
            <div class="whisker whisker2"></div>
            <div class="whisker whisker3"></div>
        </div>
        <div class="cheek left"></div>
        <div class="cheek right"></div>
    </div>
</body>
</html>