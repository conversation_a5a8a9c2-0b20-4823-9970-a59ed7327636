// Parámetros de dificultad
const niveles = {
    easy: { filas: 8, columnas: 8, minas: 10 },
    medium: { filas: 16, columnas: 16, minas: 40 },
    hard: { filas: 16, columnas: 30, minas: 99 }
};

let filas, columnas, minas, tablero, minasRestantes;
const boardElement = document.getElementById('board');
const minesCountElement = document.getElementById('mines-count');
const restartButton = document.getElementById('restart');
const difficultySelect = document.getElementById('difficulty');
const bestTimeElement = document.getElementById('best-time');
// Cargar sonidos con una función para mejorar el rendimiento
const soundReveal = document.getElementById('sound-reveal');
const soundFlag = document.getElementById('sound-flag');
const soundExplode = document.getElementById('sound-explode');

// Función para reproducir sonidos de manera más eficiente
function playSound(sound) {
    // Crear una copia del sonido para permitir reproducción simultánea
    if (sound.paused) {
        sound.currentTime = 0;
        sound.play();
    } else {
        // Si el sonido ya está reproduciéndose, crear una copia temporal
        const soundClone = sound.cloneNode();
        soundClone.volume = sound.volume;
        soundClone.play();
    }
}

let timer = null;
let startTime = null;
let playing = false;
let timerStarted = false; // NUEVA VARIABLE

function iniciarJuego() {
    const nivel = difficultySelect.value; // Asegúrate que niveles esté definido antes
    filas = niveles[nivel].filas;
    columnas = niveles[nivel].columnas;
    minas = niveles[nivel].minas;
    minasRestantes = minas;
    minesCountElement.textContent = `Minas: ${minasRestantes}`;
    playing = true;
    resetTimer();
    timerStarted = false; 
    resetCeldasDescubiertas(); 
    mostrarBestTime();
    generarTablero();
}

function mostrarBestTime() {
    const nivel = difficultySelect.value;
    const best = localStorage.getItem('bestTime-' + nivel);
    bestTimeElement.textContent = best ? `Récord: ${best}s` : '';
}

function startTimer() {
    startTime = Date.now();
    if (timer) clearInterval(timer);
    
    timer = setInterval(() => {
        const elapsed = Math.floor((Date.now() - startTime) / 1000);
        
        // Extraer el récord actual si existe
        let recordText = '';
        const recordMatch = bestTimeElement.textContent.match(/Récord: (\d+)s/);
        if (recordMatch) {
            recordText = `Récord: ${recordMatch[1]}s`;
        }
        
        // Actualizar el texto con el tiempo actual y el récord
        bestTimeElement.textContent = `Tiempo: ${elapsed}s ${recordText ? ' ' + recordText : ''}`;
    }, 1000);
}

function resetTimer() {
    if (timer) clearInterval(timer);
    bestTimeElement.textContent = '';
}

function stopTimer() {
    if (timer) clearInterval(timer);
}

function generarTablero() {
    tablero = [];
    const fragment = document.createDocumentFragment();
    boardElement.innerHTML = '';
    boardElement.style.gridTemplateRows = `repeat(${filas}, var(--cell-size))`;
    boardElement.style.gridTemplateColumns = `repeat(${columnas}, var(--cell-size))`;

    // Inicializar tablero vacío
    for (let f = 0; f < filas; f++) {
        tablero[f] = [];
        for (let c = 0; c < columnas; c++) {
            tablero[f][c] = { mina: false, descubierta: false, marcada: false, numero: 0 };
        }
    }

    // Colocar minas aleatoriamente en cualquier celda
    let posicionesTotales = filas * columnas;
    let posiciones = [];
    for (let i = 0; i < posicionesTotales; i++) {
        posiciones.push(i);
    }
    // Mezclar el array usando el algoritmo Fisher-Yates
    for (let i = posiciones.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [posiciones[i], posiciones[j]] = [posiciones[j], posiciones[i]];
    }
    // Colocar minas usando las primeras 'minas' posiciones del array mezclado
    for (let i = 0; i < minas; i++) {
        const pos = posiciones[i];
        const f = Math.floor(pos / columnas);
        const c = pos % columnas;
        tablero[f][c].mina = true;
    }
    // Calcular números
    for (let f = 0; f < filas; f++) {
        for (let c = 0; c < columnas; c++) {
            if (!tablero[f][c].mina) {
                tablero[f][c].numero = contarMinasAlrededor(f, c);
            }
        }
    }
    // Dibujar tablero
    for (let f = 0; f < filas; f++) {
        for (let c = 0; c < columnas; c++) {
            const celda = document.createElement('div');
            celda.classList.add('cell');
            celda.dataset.fila = f;
            celda.dataset.columna = c;
            celda.oncontextmenu = marcarCelda;
            celda.onclick = descubrirCelda;
            fragment.appendChild(celda);
        }
    }
    boardElement.appendChild(fragment);
}

// Función para dibujar el tablero vacío o inicializado
function dibujarTablero(callbackCelda) {
    boardElement.innerHTML = '';
    boardElement.style.gridTemplateRows = `repeat(${filas}, var(--cell-size))`;
    boardElement.style.gridTemplateColumns = `repeat(${columnas}, var(--cell-size))`;
    const fragment = document.createDocumentFragment();
    for (let f = 0; f < filas; f++) {
        for (let c = 0; c < columnas; c++) {
            const celda = document.createElement('div');
            celda.classList.add('cell');
            celda.dataset.fila = f;
            celda.dataset.columna = c;
            celda.oncontextmenu = marcarCelda;
            celda.onclick = descubrirCelda;
            if (callbackCelda) callbackCelda(celda, f, c);
            fragment.appendChild(celda);
        }
    }
    boardElement.appendChild(fragment);
}

function descubrirCelda(e) {
    if (!playing) return;
    const f = parseInt(this.dataset.fila);
    const c = parseInt(this.dataset.columna);

    // Elimina la lógica de generación de tablero en el primer clic
    if (!timerStarted) {
        startTimer();
        timerStarted = true;
    }
    if (tablero[f][c].descubierta || tablero[f][c].marcada) return;

    tablero[f][c].descubierta = true;
    celdasDescubiertas++; // Incrementar contador de celdas descubiertas

    this.classList.add('revealed');
    playSound(soundReveal);
    
    if (tablero[f][c].mina) {
        this.textContent = '💣';
        this.classList.add('mine');
        playSound(soundExplode);
        alert('¡Has perdido!');
        revelarMinas();
        stopTimer();
        playing = false;
    } else if (tablero[f][c].numero > 0) {
        this.textContent = tablero[f][c].numero;
        this.setAttribute('data-number', tablero[f][c].numero);
    } else {
        this.textContent = '';
        descubrirVecinas(f, c);
    }
    comprobarVictoria();
}

function marcarCelda(e) {
    e.preventDefault();
    if (!playing) return;
    const f = parseInt(this.dataset.fila);
    const c = parseInt(this.dataset.columna);

    if (tablero[f][c].descubierta) return;

    tablero[f][c].marcada = !tablero[f][c].marcada;

    if (tablero[f][c].marcada) {
        this.classList.add('flagged');
        this.textContent = ''; // Limpiar cualquier texto previo al marcar
        minasRestantes--;
        playSound(soundFlag);
    } else {
        this.classList.remove('flagged');
        // No es necesario restaurar texto aquí, ya que las celdas no descubiertas no muestran números.
        // Si la celda tuviera un número (aunque no debería si no está descubierta), se gestionaría al descubrir.
        minasRestantes++;
    }
    minesCountElement.textContent = `Minas: ${minasRestantes}`;
    verificarVictoria();
}

function revelarMinas() {
    for (let f = 0; f < filas; f++) {
        for (let c = 0; c < columnas; c++) {
            if (tablero[f][c].mina) {
                const celda = boardElement.children[f * columnas + c];
                celda.textContent = '💣';
                celda.classList.add('revealed', 'mine');
            }
        }
    }
}

// Variable global para llevar la cuenta de celdas descubiertas
let celdasDescubiertas = 0;

function resetCeldasDescubiertas() {
    celdasDescubiertas = 0;
}

function comprobarVictoria() {
    // Verificar si el número de celdas descubiertas es igual al total de celdas sin minas
    if (celdasDescubiertas === filas * columnas - minas) {
        stopTimer();
        playing = false;
        const elapsed = Math.floor((Date.now() - startTime) / 1000);
        alert('¡Felicidades, ganaste en ' + elapsed + ' segundos!');
        guardarBestTime(elapsed);
        mostrarBestTime();
    }
}

function guardarBestTime(time) {
    const nivel = difficultySelect.value;
    const key = 'bestTime-' + nivel;
    const best = localStorage.getItem(key);
    if (!best || time < best) {
        localStorage.setItem(key, time);
    }
}

// Eventos
restartButton.onclick = iniciarJuego;
difficultySelect.onchange = iniciarJuego;

// Iniciar juego al cargar
window.onload = iniciarJuego;


function descubrirVecinas(f, c) {
    // Usar un enfoque iterativo con una cola para evitar desbordamiento de pila
    const cola = [{f, c}];
    
    while (cola.length > 0) {
        const {f: fila, c: columna} = cola.shift();
        
        for (let df = -1; df <= 1; df++) {
            for (let dc = -1; dc <= 1; dc++) {
                let nf = fila + df, nc = columna + dc;
                if (
                    nf >= 0 && nf < filas &&
                    nc >= 0 && nc < columnas &&
                    !(nf === fila && nc === columna)
                ) {
                    const celda = boardElement.children[nf * columnas + nc];
                    if (!tablero[nf][nc].descubierta && !tablero[nf][nc].mina && !tablero[nf][nc].marcada) {
                        tablero[nf][nc].descubierta = true;
                        celdasDescubiertas++; // Incrementar contador de celdas descubiertas
                        celda.classList.add('revealed');
                        
                        if (tablero[nf][nc].numero > 0) {
                            celda.textContent = tablero[nf][nc].numero;
                            celda.setAttribute('data-number', tablero[nf][nc].numero);
                        } else {
                            celda.textContent = '';
                            // Agregar a la cola solo si es una celda vacía
                            cola.push({f: nf, c: nc});
                        }
                    }
                }
            }
        }
    }
}

function contarMinasAlrededor(f, c) {
    let total = 0;
    for (let df = -1; df <= 1; df++) {
        for (let dc = -1; dc <= 1; dc++) {
            if (df === 0 && dc === 0) continue;
            let nf = f + df;
            let nc = c + dc;
            if (nf >= 0 && nf < filas && nc >= 0 && nc < columnas) {
                if (tablero[nf][nc].mina) total++;
            }
        }
    }
    return total;
}


function revelarTablero(victoria) {
    for (let f = 0; f < filas; f++) {
        for (let c = 0; c < columnas; c++) {
            const celda = boardElement.children[f * columnas + c];
            const celdaElement = document.createElement('div');
            celdaElement.classList.add('cell');
            celdaElement.dataset.fila = f;
            celdaElement.dataset.columna = c;
            celdaElement.oncontextmenu = marcarCelda;
            celdaElement.onclick = descubrirCelda;
            if (callbackCelda) callbackCelda(celda, f, c);
            fragment.appendChild(celda);
        }
    }
    boardElement.appendChild(fragment);
}

function revelarTablero(victoria) {
    for (let f = 0; f < filas; f++) {
        for (let c = 0; c < columnas; c++) {
            const celda = boardElement.children[f * columnas + c];
            const celdaElement = document.createElement('div');
            celdaElement.classList.add('cell');
            celdaElement.dataset.fila = f;
            celdaElement.dataset.columna = c;
            celdaElement.oncontextmenu = marcarCelda;
            celdaElement.onclick = descubrirCelda;
            if (callbackCelda) callbackCelda(celda, f, c);
            fragment.appendChild(celda);
        }
    }
    boardElement.appendChild(fragment);
}

function revelarTablero(victoria) {
    for (let f = 0; f < filas; f++) {
        for (let c = 0; c < columnas; c++) {
            const celdaElement = document.createElement('div');
            celdaElement.classList.add('cell');
            celdaElement.dataset.fila = f;
            celdaElement.dataset.columna = c;
            celdaElement.oncontextmenu = marcarCelda;
            celdaElement.onclick = descubrirCelda;
            if (callbackCelda) callbackCelda(celda, f, c);
            fragment.appendChild(celda);
        }
    }
    boardElement.appendChild(fragment);
}

function revelarTablero(victoria) {
    for (let f = 0; f < filas; f++) {
        for (let c = 0; c < columnas; c++) {
            const celdaElement = document.createElement('div');
            celdaElement.classList.add('cell');
            celdaElement.dataset.fila = f;
            celdaElement.dataset.columna = c;
            celdaElement.oncontextmenu = marcarCelda;
            celdaElement.onclick = descubrirCelda;
            if (callbackCelda) callbackCelda(celda, f, c);
            fragment.appendChild(celda);
        }
    }
    boardElement.appendChild(fragment);
}

function revelarTablero(victoria) {
    for (let f = 0; f < filas; f++) {
        for (let c = 0; c < columnas; c++) {
            const celda = boardElement.children[f * columnas + c];
            const celdaElement = document.createElement('div');
            celdaElement.classList.add('cell');
            celdaElement.dataset.fila = f;
            celdaElement.dataset.columna = c;
            celdaElement.oncontextmenu = marcarCelda;
            celdaElement.onclick = descubrirCelda;
            if (callbackCelda) callbackCelda(celda, f, c);
            fragment.appendChild(celda);
        }
    }
    boardElement.appendChild(fragment);
}

function revelarTablero(victoria) {
    for (let f = 0; f < filas; f++) {
        for (let c = 0; c < columnas; c++) {
            const celdaElement = document.createElement('div');
            celdaElement.classList.add('cell');
            celdaElement.dataset.fila = f;
            celdaElement.dataset.columna = c;
            celdaElement.oncontextmenu = marcarCelda;
            celdaElement.onclick = descubrirCelda;
            if (callbackCelda) callbackCelda(celda, f, c);
            fragment.appendChild(celda);
        }
    }
    boardElement.appendChild(fragment);
}

function revelarTablero(victoria) {
    for (let f = 0; f < filas; f++) {
        for (let c = 0; c < columnas; c++) {
            const celdaElement = document.createElement('div');
            celdaElement.classList.add('cell');
            celdaElement.dataset.fila = f;
            celdaElement.dataset.columna = c;
            celdaElement.oncontextmenu = marcarCelda;
            celdaElement.onclick = descubrirCelda;
            if (callbackCelda) callbackCelda(celda, f, c);
            fragment.appendChild(celda);
        }
    }
    boardElement.appendChild(fragment);
}

function revelarTablero(victoria) {
    for (let f = 0; f < filas; f++) {
        for (let c = 0; c < columnas; c++) {
            const celdaElement = document.createElement('div');
            celdaElement.classList.add('cell');
            celdaElement.dataset.fila = f;
            celdaElement.dataset.columna = c;
            celdaElement.oncontextmenu = marcarCelda;
            celdaElement.onclick = descubrirCelda;
            if (callbackCelda) callbackCelda(celda, f, c);
            fragment.appendChild(celda);
        }
    }
    boardElement.appendChild(fragment);
}

function revelarTablero(victoria) {
    for (let f = 0; f < filas; f++) {
        for (let c = 0; c < columnas; c++) {
            const celdaElement = document.createElement('div');
            celdaElement.classList.add('cell');
            celdaElement.dataset.fila = f;
            celdaElement.dataset.columna = c;
            celdaElement.oncontextmenu = marcarCelda;
            celdaElement.onclick = descubrirCelda;
            if (callbackCelda) callbackCelda(celda, f, c);
            fragment.appendChild(celda);
        }
    }
    boardElement.appendChild(fragment);
}

function revelarTablero(victoria) {
    for (let f = 0; f < filas; f++) {
        for (let c = 0; c < columnas; c++) {
            const celdaElement = document.createElement('div');
            celdaElement.classList.add('cell');
            celdaElement.dataset.fila = f;
            celdaElement.dataset.columna = c;
            celdaElement.oncontextmenu = marcarCelda;
            celdaElement.onclick = descubrirCelda;
            if (callbackCelda) callbackCelda(celda, f, c);
            fragment.appendChild(celda);
        }
    }
    boardElement.appendChild(fragment);
}

function revelarTablero(victoria) {
    for (let f = 0; f < filas; f++) {
        for (let c = 0; c < columnas; c++) {
            const celda = boardElement.children[f * columnas + c];
            const celdaElement = document.createElement('div');
            celdaElement.classList.add('cell');
            celdaElement.dataset.fila = f;
            celdaElement.dataset.columna = c;
            celdaElement.oncontextmenu = marcarCelda;
            celdaElement.onclick = descubrirCelda;
            if (callbackCelda) callbackCelda(celda, f, c);
            fragment.appendChild(celda);
        }
    }
    boardElement.appendChild(fragment);
}

function revelarTablero(victoria) {
    for (let f = 0; f < filas; f++) {
        for (let c = 0; c < columnas; c++) {
            const celdaElement = document.createElement('div');
            celdaElement.classList.add('cell');
            celdaElement.dataset.fila = f;
            celdaElement.dataset.columna = c;
            celdaElement.oncontextmenu = marcarCelda;
            celdaElement.onclick = descubrirCelda;
            if (callbackCelda) callbackCelda(celda, f, c);
            fragment.appendChild(celda);
        }
    }
    boardElement.appendChild(fragment);
}

function revelarTablero(victoria) {
    for (let f = 0; f < filas; f++) {
        for (let c = 0; c < columnas; c++) {
            const celdaElement = document.createElement('div');
            celdaElement.classList.add('cell');
            celdaElement.dataset.fila = f;
            celdaElement.dataset.columna = c;
            celdaElement.oncontextmenu = marcarCelda;
            celdaElement.onclick = descubrirCelda;
            if (callbackCelda) callbackCelda(celda, f, c);
            fragment.appendChild(celda);
        }
    }
    boardElement.appendChild(fragment);
}

function revelarTablero(victoria) {
    for (let f = 0; f < filas; f++) {
        for (let c = 0; c < columnas; c++) {
            const celdaElement = document.createElement('div');
            celdaElement.classList.add('cell');
            celdaElement.dataset.fila = f;
            celdaElement.dataset.columna = c;
            celdaElement.oncontextmenu = marcarCelda;
            celdaElement.onclick = descubrirCelda;
            if (callbackCelda) callbackCelda(celda, f, c);
            fragment.appendChild(celda);
        }
    }
    boardElement.appendChild(fragment);
}

function revelarTablero(victoria) {
    for (let f = 0; f < filas; f++) {
        for (let c = 0; c < columnas; c++) {
            const celdaElement = document.createElement('div');
            celdaElement.classList.add('cell');
            celdaElement.dataset.fila = f;
            celdaElement.dataset.columna = c;
            celdaElement.oncontextmenu = marcarCelda;
            celdaElement.onclick = descubrirCelda;
            if (callbackCelda) callbackCelda(celda, f, c);
            fragment.appendChild(celda);
        }
    }
    boardElement.appendChild(fragment);
}

function revelarTablero(victoria) {
    for (let f = 0; f < filas; f++) {
        for (let c = 0; c < columnas; c++) {
            const celdaElement = document.createElement('div');
            celdaElement.classList.add('cell');
            celdaElement.dataset.fila = f;
            celdaElement.dataset.columna = c;
            celdaElement.oncontextmenu = marcarCelda;
            celdaElement.onclick = descubrirCelda;
            if (callbackCelda) callbackCelda(celda, f, c);
            fragment.appendChild(celda);
        }
    }
    boardElement.appendChild(fragment);
}

function revelarTablero(victoria) {
    for (let f = 0; f < filas; f++) {
        for (let c = 0; c < columnas; c++) {
            const celdaElement = document.createElement('div');
            celdaElement.classList.add('cell');
            celdaElement.dataset.fila = f;
            celdaElement.dataset.columna = c;
            celdaElement.oncontextmenu = marcarCelda;
            celdaElement.onclick = descubrirCelda;
            if (callbackCelda) callbackCelda(celda, f, c);
            fragment.appendChild(celda);
        }
    }
    boardElement.appendChild(fragment);
}

function revelarTablero(victoria) {
    for (let f = 0; f < filas; f++) {
        for (let c = 0; c < columnas; c++) {
            const celdaElement = document.createElement('div');
            celdaElement.classList.add('cell');
            celdaElement.dataset.fila = f;
            celdaElement.dataset.columna = c;
            celdaElement.oncontextmenu = marcarCelda;
            celdaElement.onclick = descubrirCelda;
            if (callbackCelda) callbackCelda(celda, f, c);
            fragment.appendChild(celda);
        }
    }
    boardElement.appendChild(fragment);
}

function revelarTablero(victoria) {
    for (let f = 0; f < filas; f++) {
        for (let c = 0; c < columnas; c++) {
            const celdaElement = document.createElement('div');
            celdaElement.classList.add('cell');
            celdaElement.dataset.fila = f;
            celdaElement.dataset.columna = c;
            celdaElement.oncontextmenu = marcarCelda;
            celdaElement.onclick = descubrirCelda;
            if (callbackCelda) callbackCelda(celda, f, c);
            fragment.appendChild(celda);
        }
    }
    boardElement.appendChild(fragment);
}

function revelarTablero(victoria) {
    for (let f = 0; f < filas; f++) {
        for (let c = 0; c < columnas; c++) {
            const celdaElement = document.createElement('div');
            celdaElement.classList.add('cell');
            celdaElement.dataset.fila = f;
            celdaElement.dataset.columna = c;
            celdaElement.oncontextmenu = marcarCelda;
            celdaElement.onclick = descubrirCelda;
            if (callbackCelda) callbackCelda(celda, f, c);
            fragment.appendChild(celda);
        }
    }
    boardElement.appendChild(fragment);
}

function revelarTablero(victoria) {
    for (let f = 0; f < filas; f++) {
        for (let c = 0; c < columnas; c++) {
            const celdaElement = document.createElement('div');
            celdaElement.classList.add('cell');
            celdaElement.dataset.fila = f;
            celdaElement.dataset.columna = c;
            celdaElement.oncontextmenu = marcarCelda;
            celdaElement.onclick = descubrirCelda;
            if (callbackCelda) callbackCelda(celda, f, c);
            fragment.appendChild(celda);
        }
    }
    boardElement.appendChild(fragment);
}

function revelarTablero(victoria) {
    for (let f = 0; f < filas; f++) {
        for (let c = 0; c < columnas; c++) {
            const celdaElement = document.createElement('div');
            celdaElement.classList.add('cell');
            celdaElement.dataset.fila = f;
            celdaElement.dataset.columna = c;
            celdaElement.oncontextmenu = marcarCelda;
            celdaElement.onclick = descubrirCelda;
            if (callbackCelda) callbackCelda(celda, f, c);
            fragment.appendChild(celda);
        }
    }
    boardElement.appendChild(fragment);
}

function revelarTablero(victoria) {
    for (let f = 0; f < filas; f++) {
        for (let c = 0; c < columnas; c++) {
            const celdaElement = document.createElement('div');
            celdaElement.classList.add('cell');
            celdaElement.dataset.fila = f;
            celdaElement.dataset.columna = c;
            celdaElement.oncontextmenu = marcarCelda;
            celdaElement.onclick = descubrirCelda;
            if (callbackCelda) callbackCelda(celda, f, c);
            fragment.appendChild(celda);
        }
    }
    boardElement.appendChild(fragment);
}

function revelarTablero(victoria) {
    for (let f = 0; f < filas; f++) {
        for (let c = 0; c < columnas; c++) {
            const celdaElement = document.createElement('div');
            celdaElement.classList.add('cell');
            celdaElement.dataset.fila = f;
            celdaElement.dataset.columna = c;
            celdaElement.oncontextmenu = marcarCelda;
            celdaElement.onclick = descubrirCelda;
            if (callbackCelda) callbackCelda(celda, f, c);
            fragment.appendChild(celda);
        }
    }
    boardElement.appendChild(fragment);
}

function revelarTablero(victoria) {
    for (let f = 0; f < filas; f++) {
        for (let c = 0; c < columnas; c++) {
            const celdaElement = document.createElement('div');
            celdaElement.classList.add('cell');
            celdaElement.dataset.fila = f;
            celdaElement.dataset.columna = c;
            celdaElement.oncontextmenu = marcarCelda;
            celdaElement.onclick = descubrirCelda;
            if (callbackCelda) callbackCelda(celda, f, c);
            fragment.appendChild(celda);
        }
    }
    boardElement.appendChild(fragment);
}

function revelarTablero(victoria) {
    for (let f = 0; f < filas; f++) {
        for (let c = 0; c < columnas; c++) {
            const celdaElement = document.createElement('div');
            celdaElement.classList.add('cell');
            celdaElement.dataset.fila = f;
            celdaElement.dataset.columna = c;
            celdaElement.oncontextmenu = marcarCelda;
            celdaElement.onclick = descubrirCelda;
            if (callbackCelda) callbackCelda(celda, f, c);
            fragment.appendChild(celda);
        }
    }
    boardElement.appendChild(fragment);
}

function revelarTablero(victoria) {
    for (let f = 0; f < filas; f++) {
        for (let c = 0; c < columnas; c++) {
            const celdaElement = document.createElement('div');
            celdaElement.classList.add('cell');
            celdaElement.dataset.fila = f;
            celdaElement.dataset.columna = c;
            celdaElement.oncontextmenu = marcarCelda;
            celdaElement.onclick = descubrirCelda;
            if (callbackCelda) callbackCelda(celda, f, c);
            fragment.appendChild(celda);
        }
    }
    boardElement.appendChild(fragment);
}

function revelarTablero(victoria) {
    for (let f = 0; f < filas; f++) {
        for (let c = 0; c < columnas; c++) {
            const celdaElement = document.createElement('div');
            celdaElement.classList.add('cell');
            celdaElement.dataset.fila = f;
            celdaElement.dataset.columna = c;
            celdaElement.oncontextmenu = marcarCelda;
            celdaElement.onclick = descubrirCelda;
            if (callbackCelda) callbackCelda(celda, f, c);
            fragment.appendChild(celda);
        }
    }
    boardElement.appendChild(fragment);
}

function revelarTablero(victoria) {
    for (let f = 0; f < filas; f++) {
        for (let c = 0; c < columnas; c++) {
            const celdaElement = document.createElement('div');
            celdaElement.classList.add('cell');
            celdaElement.dataset.fila = f;
            celdaElement.dataset.columna = c;
            celdaElement.oncontextmenu = marcarCelda;
            celdaElement.onclick = descubrirCelda;
            if (callbackCelda) callbackCelda(celda, f, c);
            fragment.appendChild(celda);
        }
    }
    boardElement.appendChild(fragment);
}

function revelarTablero(victoria) {
    for (let f = 0; f < filas; f++) {
        for (let c = 0; c < columnas; c++) {
            const celdaElement = document.createElement('div');
            celdaElement.classList.add('cell');
            celdaElement.dataset.fila = f;
            celdaElement.dataset.columna = c;
            celdaElement.oncontextmenu = marcarCelda;
            celdaElement.onclick = descubrirCelda;
            if (callbackCelda) callbackCelda(celda, f, c);
            fragment.appendChild(celda);
        }
    }
    boardElement.appendChild(fragment);
}

function revelarTablero(victoria) {
    for (let f = 0; f < filas; f++) {
        for (let c = 0; c < columnas; c++) {
            const celdaElement = document.createElement('div');
            celdaElement.classList.add('cell');
            celdaElement.dataset.fila = f;
            celdaElement.dataset.columna = c;
            celdaElement.oncontextmenu = marcarCelda;
            celdaElement.onclick = descubrirCelda;
            if (callbackCelda) callbackCelda(celda, f, c);
            fragment.appendChild(celda);
        }
    }
    boardElement.appendChild(fragment);
}

function revelarTablero(victoria) {
    for (let f = 0; f < filas; f++) {
        for (let c = 0; c < columnas; c++) {
            const celdaElement = document.createElement('div');
            celdaElement.classList.add('cell');
            celdaElement.dataset.fila = f;
            celdaElement.dataset.columna = c;
            celdaElement.oncontextmenu = marcarCelda;
            celdaElement.onclick = descubrirCelda;
            if (callbackCelda) callbackCelda(celda, f, c);
            fragment.appendChild(celda);
        }
    }
    boardElement.appendChild(fragment);
}

function revelarTablero(victoria) {
    for (let f = 0; f < filas; f++) {
        for (let c = 0; c < columnas; c++) {
            const celdaElement = document.createElement('div');
            celdaElement.classList.add('cell');
            celdaElement.dataset.fila = f;
            celdaElement.dataset.columna = c;
            celdaElement.oncontextmenu = marcarCelda;
            celdaElement.onclick = descubrirCelda;
            if (callbackCelda) callbackCelda(celda, f, c);
            fragment.appendChild(celda);
        }
    }
    boardElement.appendChild(fragment);
}

function revelarTablero(victoria) {
    for (let f = 0; f < filas; f++) {
        for (let c = 0; c < columnas; c++) {
            const celdaElement = document.createElement('div');
            celdaElement.classList.add('cell');
            celdaElement.dataset.fila = f;
            celdaElement.dataset.columna = c;
            celdaElement.oncontextmenu = marcarCelda;
            celdaElement.onclick = descubrirCelda;
            if (callbackCelda) callbackCelda(celda, f, c);
            fragment.appendChild(celda);
        }
    }
    boardElement.appendChild(fragment);
}

function revelarTablero(victoria) {
    for (let f = 0; f < filas; f++) {
        for (let c = 0; c < columnas; c++) {
            const celdaElement = document.createElement('div');
            celdaElement.classList.add('cell');
            celdaElement.dataset.fila = f;
            celdaElement.dataset.columna = c;
            celdaElement.oncontextmenu = marcarCelda;
            celdaElement.onclick = descubrirCelda;
            if (callbackCelda) callbackCelda(celda, f, c);
            fragment.appendChild(celda);
        }
    }
    boardElement.appendChild(fragment);
}

function revelarTablero(victoria) {
    for (let f = 0; f < filas; f++) {
        for (let c = 0; c < columnas; c++) {
            const celdaElement = document.createElement('div');
            celdaElement.classList.add('cell');
            celdaElement.dataset.fila = f;
            celdaElement.dataset.columna = c;
            celdaElement.oncontextmenu = marcarCelda;
            celdaElement.onclick = descubrirCelda;
            if (callbackCelda) callbackCelda(celda, f, c);
            fragment.appendChild(celda);
        }
    }
    boardElement.appendChild(fragment);
}

function revelarTablero(victoria) {
    for (let f = 0; f < filas; f++) {
        for (let c = 0; c < columnas; c++) {
            const celdaElement = document.createElement('div');
            celdaElement.classList.add('cell');
            celdaElement.dataset.fila = f;
            celdaElement.dataset.columna = c;
            celdaElement.oncontextmenu = marcarCelda;
            celdaElement.onclick = descubrirCelda;
            if (callbackCelda) callbackCelda(celda, f, c);
            fragment.appendChild(celda);
        }
    }
    boardElement.appendChild(fragment);
}

function revelarTablero(victoria) {
    for (let f = 0; f < filas; f++) {
        for (let c = 0; c < columnas; c++) {
            const celdaElement = document.createElement('div');
            celdaElement.classList.add('cell');
            celdaElement.dataset.fila = f;
            celdaElement.dataset.columna = c;
            celdaElement.oncontextmenu = marcarCelda;
            celdaElement.onclick = descubrirCelda;
            if (callbackCelda) callbackCelda(celda, f, c);
            fragment.appendChild(celda);
        }
    }
    boardElement.appendChild(fragment);
}

function revelarTablero(victoria) {
    for (let f = 0; f < filas; f++) {
        for (let c = 0; c < columnas; c++) {
            const celdaElement = document.createElement('div');
            celdaElement.classList.add('cell');
            celdaElement.dataset.fila = f;
            celdaElement.dataset.columna = c;
            celdaElement.oncontextmenu = marcarCelda;
            celdaElement.onclick = descubrirCelda;
            if (callbackCelda) callbackCelda(celda, f, c);
            fragment.appendChild(celda);
        }
    }
    boardElement.appendChild(fragment);
}

function revelarTablero(victoria) {
    for (let f = 0; f < filas; f++) {
        for (let c = 0; c < columnas; c++) {
            const celdaElement = document.createElement('div');
            celdaElement.classList.add('cell');
            celdaElement.dataset.fila = f;
            celdaElement.dataset.columna = c;
            celdaElement.oncontextmenu = marcarCelda;
            celdaElement.onclick = descubrirCelda;
            if (callbackCelda) callbackCelda(celda, f, c);
            fragment.appendChild(celda);
        }
    }
    boardElement.appendChild(fragment);
}

function revelarTablero(victoria) {
    for (let f = 0; f < filas; f++) {
        for (let c = 0; c < columnas; c++) {
            const celdaElement = document.createElement('div');
            celdaElement.classList.add('cell');
            celdaElement.dataset.fila = f;
            celdaElement.dataset.columna = c;
            celdaElement.oncontextmenu = marcarCelda;
            celdaElement.onclick = descubrirCelda;
            if (callbackCelda) callbackCelda(celda, f, c);
            fragment.appendChild(celda);
        }
    }
    boardElement.appendChild(fragment);
}

function revelarTablero(victoria) {
    for (let f = 0; f < filas; f++) {
        for (let c = 0; c < columnas; c++) {
            const celdaElement = document.createElement('div');
            celdaElement.classList.add('cell');
            celdaElement.dataset.fila = f;
            celdaElement.dataset.columna = c;
            celdaElement.oncontextmenu = marcarCelda;
            celdaElement.onclick = descubrirCelda;
            if (callbackCelda) callbackCelda(celda, f, c);
            fragment.appendChild(celda);
        }
    }
    boardElement.appendChild(fragment);
}

function revelarTablero(victoria) {
    for (let f = 0; f < filas; f++) {
        for (let c = 0; c < columnas; c++) {
            const celdaElement = document.createElement('div');
            celdaElement.classList.add('cell');
            celdaElement.dataset.fila = f;
            celdaElement.dataset.columna = c;
            celdaElement.oncontextmenu = marcarCelda;
            celdaElement.onclick = descubrirCelda;
            if (callbackCelda) callbackCelda(celda, f, c);
            fragment.appendChild(celda);
        }
    }
    boardElement.appendChild(fragment);
}

function revelarTablero(victoria) {
    for (let f = 0; f < filas; f++) {
        for (let c = 0; c < columnas; c++) {
            const celdaElement = document.createElement('div');
            celdaElement.classList.add('cell');
            celdaElement.dataset.fila = f;
            celdaElement.dataset.columna = c;
            celdaElement.oncontextmenu = marcarCelda;
            celdaElement.onclick = descubrirCelda;
            if (callbackCelda) callbackCelda(celda, f, c);
            fragment.appendChild(celda);
        }
    }
    boardElement.appendChild(fragment);
}

function revelarTablero(victoria) {
    for (let f = 0; f < filas; f++) {
        for (let c = 0; c < columnas; c++) {
            const celdaElement = document.createElement('div');
            celdaElement.classList.add('cell');
            celdaElement.dataset.fila = f;
            celdaElement.dataset.columna = c;
            celdaElement.oncontextmenu = marcarCelda;
            celdaElement.onclick = descubrirCelda;
            if (callbackCelda) callbackCelda(celda, f, c);
            fragment.appendChild(celda);
        }
    }
    boardElement.appendChild(fragment);
}

function revelarTablero(victoria) {
    for (let f = 0; f < filas; f++) {
        for (let c = 0; c < columnas; c++) {
            const celdaElement = document.createElement('div');
            celdaElement.classList.add('cell');
            celdaElement.dataset.fila = f;
            celdaElement.dataset.columna = c;
            celdaElement.oncontextmenu = marcarCelda;
            celdaElement.onclick = descubrirCelda;
            if (callbackCelda) callbackCelda(celda, f, c);
            fragment.appendChild(celda);
        }
    }
    boardElement.appendChild(fragment);
}

function revelarTablero(victoria) {
    for (let f = 0; f < filas; f++) {
        for (let c = 0; c < columnas; c++) {
            const celdaElement = document.createElement('div');
            celdaElement.classList.add('cell');
            celdaElement.dataset.fila = f;
            celdaElement.dataset.columna = c;
            celdaElement.oncontextmenu = marcarCelda;
            celdaElement.onclick = descubrirCelda;
            if (callbackCelda) callbackCelda(celda, f, c);
            fragment.appendChild(celda);
        }
    }
    boardElement.appendChild(fragment);
}

function revelarTablero(victoria) {
    for (let f = 0; f < filas; f++) {
        for (let c = 0; c < columnas; c++) {
            const celdaElement = document.createElement('div');
            celdaElement.classList.add('cell');
            celdaElement.dataset.fila = f;
            celdaElement.dataset.columna = c;
            celdaElement.oncontextmenu = marcarCelda;
            celdaElement.onclick = descubrirCelda;
            if (callbackCelda) callbackCelda(celda, f, c);
            fragment.appendChild(celda);
        }
    }
    boardElement.appendChild(fragment);
}

function revelarTablero(victoria) {
    for (let f = 0; f < filas; f++) {
        for (let c = 0; c < columnas; c++) {
            const celdaElement = document.createElement('div');
            celdaElement.classList.add('cell');
            celdaElement.dataset.fila = f;
            celdaElement.dataset.columna = c;
            celdaElement.oncontextmenu = marcarCelda;
            celdaElement.onclick = descubrirCelda;
            if (callbackCelda) callbackCelda(celda, f, c);
            fragment.appendChild(celda);
        }
    }
    boardElement.appendChild(fragment);
}

function revelarTablero(victoria) {
    for (let f = 0; f < filas; f++) {
        for (let c = 0; c < columnas; c++) {
            const celdaElement = document.createElement('div');
            celdaElement.classList.add('cell');
            celdaElement.dataset.fila = f;
            celdaElement.dataset.columna = c;
            celdaElement.oncontextmenu = marcarCelda;
            celdaElement.onclick = descubrirCelda;
            if (callbackCelda) callbackCelda(celda, f, c);
            fragment.appendChild(celda);
        }
    }
    boardElement.appendChild(fragment);
}

function revelarTablero(victoria) {
    for (let f = 0; f < filas; f++) {
        for (let c = 0; c < columnas; c++) {
            const celdaElement = document.createElement('div');
            celdaElement.classList.add('cell');
            celdaElement.dataset.fila = f;
            celdaElement.dataset.columna = c;
            celdaElement.oncontextmenu = marcarCelda;
            celdaElement.onclick = descubrirCelda;
            if (callbackCelda) callbackCelda(celda, f, c);
            fragment.appendChild(celda);
        }
    }
    boardElement.appendChild(fragment);
}

function revelarTablero(victoria) {
    for (let f = 0; f < filas; f++) {
        for (let c = 0; c < columnas; c++) {
            const celdaElement = document.createElement('div');
            celdaElement.classList.add('cell');
            celdaElement.dataset.fila = f;
            celdaElement.dataset.columna = c;
            celdaElement.oncontextmenu = marcarCelda;
            celdaElement.onclick = descubrirCelda;
            if (callbackCelda) callbackCelda(celda, f, c);
            fragment.appendChild(celda);
        }
    }
    boardElement.appendChild(fragment);
}

function revelarTablero(victoria) {
    for (let f = 0; f < filas; f++) {
        for (let c = 0; c < columnas; c++) {
            const celdaElement = document.createElement('div');
            celdaElement.classList.add('cell');
            celdaElement.dataset.fila = f;
            celdaElement.dataset.columna = c;
            celdaElement.oncontextmenu = marcarCelda;
            celdaElement.onclick = descubrirCelda;
            if (callbackCelda) callbackCelda(celda, f, c);
            fragment.appendChild(celda);
        }
    }
    boardElement.appendChild(fragment);
}

function revelarTablero(victoria) {
    for (let f = 0; f < filas; f++) {
        for (let c = 0; c < columnas; c++) {
            const celdaElement = document.createElement('div');
            celdaElement.classList.add('cell');
            celdaElement.dataset.fila = f;
            celdaElement.dataset.columna = c;
            celdaElement.oncontextmenu = marcarCelda;
            celdaElement.onclick = descubrirCelda;
            if (callbackCelda) callbackCelda(celda, f, c);
            fragment.appendChild(celda);
        }
    }
    boardElement.appendChild(fragment);
}

function revelarTablero(victoria) {
    for (let f = 0; f < filas; f++) {
        for (let c = 0; c < columnas; c++) {
            const celdaElement = document.createElement('div');
            celdaElement.classList.add('cell');
            celdaElement.dataset.fila = f;
            celdaElement.dataset.columna = c;
            celdaElement.oncontextmenu = marcarCelda;
            celdaElement.onclick = descubrirCelda;
            if (callbackCelda) callbackCelda(celda, f, c);
            fragment.appendChild(celda);
        }
    }
    boardElement.appendChild(fragment);
}

function revelarTablero(victoria) {
    for (let f = 0; f < filas; f++) {
        for (let c = 0; c < columnas; c++) {
            const celdaElement = document.createElement('div');
            celdaElement.classList.add('cell');
            celdaElement.dataset.fila = f;
            celdaElement.dataset.columna = c;
            celdaElement.oncontextmenu = marcarCelda;
            celdaElement.onclick = descubrirCelda;
            if (callbackCelda) callbackCelda(celda, f, c);
            fragment.appendChild(celda);
        }
    }
    boardElement.appendChild(fragment);
}

function revelarTablero(victoria) {
    for (let f = 0; f < filas; f++) {
        for (let c = 0; c < columnas; c++) {
            const celdaElement = document.createElement('div');
            celdaElement.classList.add('cell');
            celdaElement.dataset.fila = f;
            celdaElement.dataset.columna = c;
            celdaElement.oncontextmenu = marcarCelda;
            celdaElement.onclick = descubrirCelda;
            if (callbackCelda) callbackCelda(celda, f, c);
            fragment.appendChild(celda);
        }
    }
    boardElement.appendChild(fragment);
}

function revelarTablero(victoria) {
    for (let f = 0; f < filas; f++) {
        for (let c = 0; c < columnas; c++) {
            const celdaElement = document.createElement('div');
            celdaElement.classList.add('cell');
            celdaElement.dataset.fila = f;
            celdaElement.dataset.columna = c;
            celdaElement.oncontextmenu = marcarCelda;
            celdaElement.onclick = descubrirCelda;
            if (callbackCelda) callbackCelda(celda, f, c);
            fragment.appendChild(celda);
        }
    }
    boardElement.appendChild(fragment);
}

function revelarTablero(victoria) {
    for (let f = 0; f < filas; f++) {
        for (let c = 0; c < columnas; c++) {
            const celdaElement = document.createElement('div');
            celdaElement.classList.add('cell');
            celdaElement.dataset.fila = f;
            celdaElement.dataset.columna = c;
            celdaElement.oncontextmenu = marcarCelda;
            celdaElement.onclick = descubrirCelda;
            if (callbackCelda) callbackCelda(celda, f, c);
            fragment.appendChild(celda);
        }
    }
    boardElement.appendChild(fragment);
}

function revelarTablero(victoria) {
    for (let f = 0; f < filas; f++) {
        for (let c = 0; c < columnas; c++) {
            const celdaElement = document.createElement('div');
            celdaElement.classList.add('cell');
            celdaElement.dataset.fila = f;
            celdaElement.dataset.columna = c;
            celdaElement.oncontextmenu = marcarCelda;
            celdaElement.onclick = descubrirCelda;
            if (callbackCelda) callbackCelda(celda, f, c);
            fragment.appendChild(celda);
        }
    }
    boardElement.appendChild(fragment);
}

function revelarTablero(victoria) {
    for (let f = 0; f < filas; f++) {
        for (let c = 0; c < columnas; c++) {
            const celdaElement = document.createElement('div');
            celdaElement.classList.add('cell');
            celdaElement.dataset.fila = f;
            celdaElement.dataset.columna = c;
            celdaElement.oncontextmenu = marcarCelda;
            celdaElement.onclick = descubrirCelda;
            if (callbackCelda) callbackCelda(celda, f, c);
            fragment.appendChild(celda);
        }
    }
    boardElement.appendChild(fragment);
}

function revelarTablero(victoria) {
    for (let f = 0; f < filas; f++) {
        for (let c = 0; c < columnas; c++) {
            const celdaElement = document.createElement('div');
            celdaElement.classList.add('cell');
            celdaElement.dataset.fila = f;
            celdaElement.dataset.columna = c;
            celdaElement.oncontextmenu = marcarCelda;
            celdaElement.onclick = descubrirCelda;
            if (callbackCelda) callbackCelda(celda, f, c);
            fragment.appendChild(celda);
        }
    }
    boardElement.appendChild(fragment);
}

function revelarTablero(victoria) {
    for (let f = 0; f < filas; f++) {
        for (let c = 0; c < columnas; c++) {
            const celdaElement = document.createElement('div');
            celdaElement.classList.add('cell');
            celdaElement.dataset.fila = f;
            celdaElement.dataset.columna = c;
            celdaElement.oncontextmenu = marcarCelda;
            celdaElement.onclick = descubrirCelda;
            if (callbackCelda) callbackCelda(celda, f, c);
            fragment.appendChild(celda);
        }
    }
    boardElement.appendChild(fragment);
}

function revelarTablero(victoria) {
    for (let f = 0; f < filas; f++) {
        for (let c = 0; c < columnas; c++) {
            const celdaElement = document.createElement('div');
            celdaElement.classList.add('cell');
            celdaElement.dataset.fila = f;
            celdaElement.dataset.columna = c;
            celdaElement.oncontextmenu = marcarCelda;
            celdaElement.onclick = descubrirCelda;
            if (callbackCelda) callbackCelda(celda, f, c);
            fragment.appendChild(celda);
        }
    }
    boardElement.appendChild(fragment);
}

function revelarTablero(victoria) {
    for (let f = 0; f < filas; f++) {
        for (let c = 0; c < columnas; c++) {
            const celdaElement = document.createElement('div');
            celdaElement.classList.add('cell');
            celdaElement.dataset.fila = f;
            celdaElement.dataset.columna = c;
            celdaElement.oncontextmenu = marcarCelda;
            celdaElement.onclick = descubrirCelda;
            if (callbackCelda) callbackCelda(celda, f, c);
            fragment.appendChild(celda);
        }
    }
    boardElement.appendChild(fragment);
}

function revelarTablero(victoria) {
    for (let f = 0; f < filas; f++) {
        for (let c = 0; c < columnas; c++) {
            const celdaElement = document.createElement('div');
            celdaElement.classList.add('cell');
            celdaElement.dataset.fila = f;
            celdaElement.dataset.columna = c;
            celdaElement.oncontextmenu = marcarCelda;
            celdaElement.onclick = descubrirCelda;
            if (callbackCelda) callbackCelda(celda, f, c);
            fragment.appendChild(celda);
        }
    }
    boardElement.appendChild(fragment);
}

function revelarTablero(victoria) {
    for (let f = 0; f < filas; f++) {
        for (let c = 0; c < columnas; c++) {
            const celdaElement = document.createElement('div');
            celdaElement.classList.add('cell');
            celdaElement.dataset.fila = f;
            celdaElement.dataset.columna = c;
            celdaElement.oncontextmenu = marcarCelda;
            celdaElement.onclick = descubrirCelda;
            if (callbackCelda) callbackCelda(celda, f, c);
            fragment.appendChild(celda);
        }
    }
    boardElement.appendChild(fragment);
}

function revelarTablero(victoria) {
    for (let f = 0; f < filas; f++) {
        for (let c = 0; c < columnas; c++) {
            const celdaElement = document.createElement('div');
            celdaElement.classList.add('cell');
            celdaElement.dataset.fila = f;
            celdaElement.dataset.columna = c;
            celdaElement.oncontextmenu = marcarCelda;
            celdaElement.onclick = descubrirCelda;
            if (callbackCelda) callbackCelda(celda, f, c);
            fragment.appendChild(celda);
        }
    }
    boardElement.appendChild(fragment);
}

function revelarTablero(victoria) {
    for (let f = 0; f < filas; f++) {
        for (let c = 0; c < columnas; c++) {
            const celdaElement = document.createElement('div');
            celdaElement.classList.add('cell');
            celdaElement.dataset.fila = f;
            celdaElement.dataset.columna = c;
            celdaElement.oncontextmenu = marcarCelda;
            celdaElement.onclick = descubrirCelda;
            if (callbackCelda) callbackCelda(celda, f, c);
            fragment.appendChild(celda);
        }
    }
    boardElement.appendChild(fragment);
}

function revelarTablero(victoria) {
    for (let f = 0; f < filas; f++) {
        for (let c = 0; c < columnas; c++) {
            const celdaElement = document.createElement('div');
            celdaElement.classList.add('cell');
            celdaElement.dataset.fila = f;
            celdaElement.dataset.columna = c;
            celdaElement.oncontextmenu = marcarCelda;
            celdaElement.onclick = descubrirCelda;
            if (callbackCelda) callbackCelda(celda, f, c);
            fragment.appendChild(celda);
        }
    }
    boardElement.appendChild(fragment);
}

function revelarTablero(victoria) {
    for (let f = 0; f < filas; f++) {
        for (let c = 0; c < columnas; c++) {
            const celdaElement = document.createElement('div');
            celdaElement.classList.add('cell');
            celdaElement.dataset.fila = f;
            celdaElement.dataset.columna = c;
            celdaElement.oncontextmenu = marcarCelda;
            celdaElement.onclick = descubrirCelda;
            if (callbackCelda) callbackCelda(celda, f, c);
            fragment.appendChild(celda);
        }
    }
    boardElement.appendChild(fragment);
}

function revelarTablero(victoria) {
    for (let f = 0; f < filas; f++) {
        for (let c = 0; c < columnas; c++) {
            const celdaElement = document.createElement('div');
            celdaElement.classList.add('cell');
            celdaElement.dataset.fila = f;
            celdaElement.dataset.columna = c;
            celdaElement.oncontextmenu = marcarCelda;
            celdaElement.onclick = descubrirCelda;
            if (callbackCelda) callbackCelda(celda, f, c);
            fragment.appendChild(celda);
        }
    }
    boardElement.appendChild(fragment);
}

function revelarTablero(victoria) {
    for (let f = 0; f < filas; f++) {
        for (let c = 0; c < columnas; c++) {
            const celdaElement = document.createElement('div');
            celdaElement.classList.add('cell');
            celdaElement.dataset.fila = f;
            celdaElement.dataset.columna = c;
            celdaElement.oncontextmenu = marcarCelda;
            celdaElement.onclick = descubrirCelda;
            if (callbackCelda) callbackCelda(celda, f, c);
            fragment.appendChild(celda);
        }
    }
    boardElement.appendChild(fragment);
}

function revelarTablero(victoria) {
    for (let f = 0; f < filas; f++) {
        for (let c = 0; c < columnas; c++) {
            const celdaElement = document.createElement('div');
            celdaElement.classList.add('cell');
            celdaElement.dataset.fila = f;
            celdaElement.dataset.columna = c;
            celdaElement.oncontextmenu = marcarCelda;
            celdaElement.onclick = descubrirCelda;
            if (callbackCelda) callbackCelda(celda, f, c);
            fragment.appendChild(celda);
        }
    }
    boardElement.appendChild(fragment);
}

function revelarTablero(victoria) {
    for (let f = 0; f < filas; f++) {
        for (let c = 0; c < columnas; c++) {
            const celdaElement = document.createElement('div');
            celdaElement.classList.add('cell');
            celdaElement.dataset.fila = f;
            celdaElement.dataset.columna = c;
            celdaElement.oncontextmenu = marcarCelda;
            celdaElement.onclick = descubrirCelda;
            if (callbackCelda) callbackCelda(celda, f, c);
            fragment.appendChild(celda);
        }
    }
    boardElement.appendChild(fragment);
}

function revelarTablero(victoria) {
    for (let f = 0; f < filas; f++) {
        for (let c = 0; c < columnas; c++) {
            const celdaElement = document.createElement('div');
            celdaElement.classList.add('cell');
            celdaElement.dataset.fila = f;
            celdaElement.dataset.columna = c;
            celdaElement.oncontextmenu = marcarCelda;
            celdaElement.onclick = descubrirCelda;
            if (callbackCelda) callbackCelda(celda, f, c);
            fragment.appendChild(celda);
        }
    }
    boardElement.appendChild(fragment);
}

function revelarTablero(victoria) {
    for (let f = 0; f < filas; f++) {
        for (let c = 0; c < columnas; c++) {
            const celdaElement = document.createElement('div');
            celdaElement.classList.add('cell');
            celdaElement.dataset.fila = f;
            celdaElement.dataset.columna = c;
            celdaElement.oncontextmenu = marcarCelda;
            celdaElement.onclick = descubrirCelda;
            if (callbackCelda) callbackCelda(celda, f, c);
            fragment.appendChild(celda);
        }
    }
    boardElement.appendChild(fragment);
}

function revelarTablero(victoria) {
    for (let f = 0; f < filas; f++) {
        for (let c = 0; c < columnas; c++) {
            const celdaElement = document.createElement('div');
            celdaElement.classList.add('cell');
            celdaElement.dataset.fila = f;
            celdaElement.dataset.columna = c;
            celdaElement.oncontextmenu = marcarCelda;
            celdaElement.onclick = descubrirCelda;
            if (callbackCelda) callbackCelda(celda, f, c);
            fragment.appendChild(celda);
        }
    }
    boardElement.appendChild(fragment);
}
